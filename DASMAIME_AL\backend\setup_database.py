#!/usr/bin/env python
"""
<PERSON>ript për të konfiguruar bazën e të dhënave me të dhëna fillestare
"""
import os
import sys
import django
from django.core.management import execute_from_command_line

def setup_database():
    """Konfiguron bazën e të dhënave me të dhëna fillestare"""
    
    # Konfiguro Django
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dasmaime_project.settings')
    django.setup()
    
    print("🚀 Duke konfiguruar bazën e të dhënave...")
    
    # 1. Ekzekuto migrimet
    print("\n📦 Duke ekzekutuar migrimet...")
    try:
        execute_from_command_line(['manage.py', 'migrate'])
        print("✅ Migrimet u ekzekutuan me sukses!")
    except Exception as e:
        print(f"❌ Gabim gjatë migrimit: {e}")
        return False
    
    # 2. K<PERSON>jon superuser
    print("\n👤 Duke krijuar admin user...")
    try:
        execute_from_command_line(['manage.py', 'create_admin'])
        print("✅ Admin user u krijua me sukses!")
    except Exception as e:
        print(f"❌ Gabim gjatë krijimit të admin: {e}")
    
    # 3. Popullon të dhënat shembull
    print("\n📊 Duke populuar të dhënat shembull...")
    try:
        execute_from_command_line(['manage.py', 'populate_sample_data'])
        print("✅ Të dhënat shembull u shtuan me sukses!")
    except Exception as e:
        print(f"❌ Gabim gjatë populimit të të dhënave: {e}")
    
    print("\n🎉 Konfigurimi i bazës së të dhënave u përfundua!")
    print("\n📋 Informacione të dobishme:")
    print("   • Admin Panel: http://localhost:8000/admin/")
    print("   • Username: admin")
    print("   • Password: admin123")
    print("   • API Base URL: http://localhost:8000/api/")
    
    print("\n🚀 Për të startuar serverin:")
    print("   python manage.py runserver")
    
    return True

if __name__ == '__main__':
    setup_database()
