@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles */
@layer base {
  html {
    font-family: Inter, system-ui, sans-serif;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: Poppins, system-ui, sans-serif;
  }
}

@layer components {
  .btn-primary {
    @apply bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 ease-in-out shadow-md hover:shadow-lg;
  }

  .btn-secondary {
    @apply bg-gradient-to-r from-secondary-500 to-secondary-600 hover:from-secondary-600 hover:to-secondary-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 ease-in-out shadow-md hover:shadow-lg;
  }

  .btn-outline {
    @apply border border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 ease-in-out;
  }

  .card {
    @apply bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden hover:shadow-xl transition-all duration-300 hover:border-primary-200;
  }

  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
  }

  .section-title {
    @apply text-2xl md:text-3xl font-heading font-semibold text-gray-900 mb-6;
  }

  .vendor-card {
    @apply card hover:shadow-xl transition-all duration-300 cursor-pointer hover:scale-105 hover:border-secondary-200;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
}

/* Loading animations */
.loading-spinner {
  border: 2px solid #f3f3f3;
  border-top: 2px solid #ec4899;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}