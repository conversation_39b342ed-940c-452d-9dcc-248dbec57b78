import api from './api'

export const vendorService = {
  // Merr listen e furnitorëve
  async getVendors(params = {}) {
    try {
      const response = await api.get('/api/vendors/', { params })
      return response.data
    } catch (error) {
      console.error('Error fetching vendors:', error)
      throw error.response?.data || error
    }
  },

  // Merr detajet e një furnitori
  async getVendor(id) {
    try {
      const response = await api.get(`/api/vendors/${id}/`)
      return response.data
    } catch (error) {
      console.error('Error fetching vendor:', error)
      throw error.response?.data || error
    }
  },

  // Merr furnitorët e veçantë
  async getFeaturedVendors() {
    try {
      const response = await api.get('/api/vendors/featured/')
      return response.data
    } catch (error) {
      console.error('Error fetching featured vendors:', error)
      throw error.response?.data || error
    }
  },

  // Merr furnitorët sipas kategorive
  async getVendorsByCategory() {
    try {
      const response = await api.get('/api/vendors/by_category/')
      return response.data
    } catch (error) {
      console.error('Error fetching vendors by category:', error)
      throw error.response?.data || error
    }
  },

  // Merr/Krijon/Përditëson profilin e furnitorit
  async getMyProfile() {
    try {
      const response = await api.get('/api/vendors/my_profile/')
      return response.data
    } catch (error) {
      console.error('Error fetching vendor profile:', error)
      throw error.response?.data || error
    }
  },

  async createMyProfile(profileData) {
    try {
      const response = await api.post('/api/vendors/my_profile/', profileData)
      return response.data
    } catch (error) {
      console.error('Error creating vendor profile:', error)
      throw error.response?.data || error
    }
  },

  async updateMyProfile(profileData) {
    try {
      const response = await api.put('/api/vendors/my_profile/', profileData)
      return response.data
    } catch (error) {
      console.error('Error updating vendor profile:', error)
      throw error.response?.data || error
    }
  },

  // Portfolio images
  async uploadPortfolioImage(imageData) {
    try {
      const formData = new FormData()
      formData.append('image', imageData.image)
      if (imageData.caption) formData.append('caption', imageData.caption)
      if (imageData.is_cover) formData.append('is_cover', imageData.is_cover)
      if (imageData.order) formData.append('order', imageData.order)

      const response = await api.post('/api/portfolio-images/', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      })
      return response.data
    } catch (error) {
      console.error('Error uploading portfolio image:', error)
      throw error.response?.data || error
    }
  },

  async deletePortfolioImage(imageId) {
    try {
      await api.delete(`/api/portfolio-images/${imageId}/`)
    } catch (error) {
      console.error('Error deleting portfolio image:', error)
      throw error.response?.data || error
    }
  }
}