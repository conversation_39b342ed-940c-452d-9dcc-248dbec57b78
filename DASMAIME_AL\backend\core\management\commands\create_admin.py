from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model

User = get_user_model()


class Command(BaseCommand):
    help = 'Krijon një superuser admin për platformën DasmalmeAL'

    def handle(self, *args, **options):
        self.stdout.write('Duke krijuar superuser admin...')
        
        # Kontrollon nëse admin ekziston tashmë
        if User.objects.filter(username='admin').exists():
            self.stdout.write(
                self.style.WARNING('Superuser "admin" ekziston tashmë.')
            )
            return
        
        # Krijon superuser
        admin_user = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            first_name='Admin',
            last_name='DasmalmeAL',
            user_type='couple'
        )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'✓ Superuser "admin" u krijua me sukses!'
            )
        )
        
        self.stdout.write(
            self.style.SUCCESS(
                '\nTë dhënat e login:'
            )
        )
        self.stdout.write('Username: admin')
        self.stdout.write('Password: admin123')
        self.stdout.write('Email: <EMAIL>')
        
        self.stdout.write(
            self.style.SUCCESS(
                '\nMund të hyni në admin panel në: http://localhost:8000/admin/'
            )
        )
