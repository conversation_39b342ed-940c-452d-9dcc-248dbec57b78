<template>
  <div class="vendor-detail-page">
    <div v-if="loading" class="flex justify-center items-center min-h-screen">
      <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
    </div>

    <div v-else-if="error" class="container mx-auto px-4 py-8 text-center">
      <h2 class="text-2xl font-bold text-red-600 mb-4">Gabim gjatë ngarkimit të të dhënave</h2>
      <p class="mb-4">{{ error }}</p>
      <button @click="fetchVendor" class="bg-primary-600 text-white px-4 py-2 rounded hover:bg-primary-700">
        Provo p<PERSON><PERSON>ë<PERSON>
      </button>
    </div>

    <div v-else-if="vendor" class="container mx-auto px-4 py-8">
      <!-- Header Section -->
      <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <div class="relative h-64 bg-gradient-to-r from-primary-500 to-primary-700">
          <div class="absolute inset-0 flex items-center justify-center">
            <h1 class="text-4xl font-bold text-white">{{ vendor.business_name }}</h1>
          </div>
        </div>

        <div class="p-6">
          <div class="flex flex-wrap items-center mb-4">
            <span class="bg-primary-100 text-primary-800 text-sm font-medium px-3 py-1 rounded-full mr-2 mb-2">
              {{ vendor.category.name }}
            </span>
            <span class="bg-gray-100 text-gray-800 text-sm font-medium px-3 py-1 rounded-full mr-2 mb-2">
              {{ vendor.city }}
            </span>
          </div>

          <p class="text-gray-700 mb-6">{{ vendor.description }}</p>

          <div class="flex flex-wrap items-center justify-between">
            <div class="flex items-center mb-4 md:mb-0">
              <div class="flex items-center">
                <span class="text-yellow-400 mr-1">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                </span>
                <span class="font-bold text-gray-900">{{ vendor.average_rating || 'N/A' }}</span>
                <span class="text-gray-600 ml-1">({{ vendor.review_count || 0 }} vlerësime)</span>
              </div>
            </div>

            <div class="flex flex-wrap">
              <a :href="`tel:${vendor.phone}`" class="bg-primary-600 text-white px-4 py-2 rounded mr-2 mb-2 hover:bg-primary-700 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                </svg>
                Telefono
              </a>
              <a :href="`mailto:${vendor.email}`" class="bg-secondary-600 text-white px-4 py-2 rounded mb-2 hover:bg-secondary-700 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                  <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                </svg>
                Email
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Portfolio Section -->
      <div v-if="vendor.portfolio_images && vendor.portfolio_images.length > 0" class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <h2 class="text-2xl font-bold p-6 border-b">Portofoli</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-6">
          <div v-for="image in vendor.portfolio_images" :key="image.id" class="overflow-hidden rounded-lg h-64">
            <img :src="image.image" :alt="vendor.business_name" class="w-full h-full object-cover hover:scale-105 transition-transform duration-300">
          </div>
        </div>
      </div>

      <!-- Reviews Section -->
      <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <h2 class="text-2xl font-bold p-6 border-b">Vlerësimet</h2>

        <div v-if="vendor.reviews && vendor.reviews.length > 0" class="divide-y">
          <div v-for="review in vendor.reviews" :key="review.id" class="p-6">
            <div class="flex items-center mb-2">
              <div class="flex items-center mr-4">
                <span class="text-yellow-400 mr-1">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                </span>
                <span class="font-bold text-gray-900">{{ review.rating }}</span>
              </div>
              <span class="font-medium text-gray-900">{{ review.user_name }}</span>
              <span class="text-gray-500 text-sm ml-auto">{{ formatDate(review.created_at) }}</span>
            </div>
            <p class="text-gray-700">{{ review.comment }}</p>
          </div>
        </div>

        <div v-else class="p-6 text-center text-gray-500">
          Nuk ka ende vlerësime për këtë furnitor.
        </div>
      </div>
    </div>

    <div v-else class="container mx-auto px-4 py-8 text-center">
      <h2 class="text-2xl font-bold text-gray-600 mb-4">Furnitori nuk u gjet</h2>
      <router-link to="/" class="bg-primary-600 text-white px-4 py-2 rounded hover:bg-primary-700">
        Kthehu në faqen kryesore
      </router-link>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useVendorStore } from '@/stores/vendor'

export default {
  name: 'VendorDetail',
  setup() {
    const route = useRoute()
    const vendorStore = useVendorStore()

    const vendor = ref(null)
    const loading = ref(true)
    const error = ref(null)

    const fetchVendor = async () => {
      loading.value = true
      error.value = null

      try {
        const vendorId = route.params.id
        const data = await vendorStore.fetchVendor(vendorId)
        vendor.value = data
      } catch (err) {
        error.value = 'Ndodhi një gabim gjatë ngarkimit të të dhënave të furnitorit. Ju lutemi provoni përsëri.'
        console.error('Error fetching vendor:', err)
      } finally {
        loading.value = false
      }
    }

    const formatDate = (dateString) => {
      const date = new Date(dateString)
      return new Intl.DateTimeFormat('sq-AL', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }).format(date)
    }

    onMounted(() => {
      fetchVendor()
    })

    return {
      vendor,
      loading,
      error,
      fetchVendor,
      formatDate
    }
  }
}
</script>
