<template>
  <div class="vendor-dashboard">
    <div class="bg-white shadow">
      <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <h1 class="text-3xl font-bold text-gray-900">Paneli i Furnitorit</h1>
      </div>
    </div>
    
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <!-- Loading state -->
      <div v-if="loading" class="flex justify-center items-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
      </div>
      
      <!-- Error state -->
      <div v-else-if="error" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert">
        <span class="block sm:inline">{{ error }}</span>
        <button @click="fetchVendorProfile" class="mt-3 bg-red-100 text-red-800 px-3 py-1 rounded">Provo përsëri</button>
      </div>
      
      <!-- Content -->
      <div v-else class="grid grid-cols-1 gap-6 lg:grid-cols-3">
        <!-- Profile Summary -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">Profili i Biznesit</h3>
            
            <div v-if="vendorProfile" class="mt-4">
              <div class="flex items-center">
                <div class="flex-shrink-0 h-16 w-16 bg-primary-100 rounded-full flex items-center justify-center">
                  <span class="text-2xl font-bold text-primary-700">{{ vendorProfile.business_name.charAt(0) }}</span>
                </div>
                <div class="ml-4">
                  <h4 class="text-lg font-bold text-gray-900">{{ vendorProfile.business_name }}</h4>
                  <p class="text-sm text-gray-500">{{ vendorProfile.category?.name || 'Pa kategori' }}</p>
                </div>
              </div>
              
              <div class="mt-4 space-y-2">
                <div class="flex items-center text-sm text-gray-500">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  {{ vendorProfile.city || 'Vendndodhja nuk është vendosur' }}
                </div>
                
                <div class="flex items-center text-sm text-gray-500">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                  {{ vendorProfile.email }}
                </div>
                
                <div class="flex items-center text-sm text-gray-500">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                  </svg>
                  {{ vendorProfile.phone || 'Numri i telefonit nuk është vendosur' }}
                </div>
              </div>
              
              <div class="mt-5">
                <button 
                  @click="showEditProfileModal = true" 
                  class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Modifiko Profilin
                </button>
              </div>
            </div>
            
            <div v-else class="mt-4 text-center py-4">
              <p class="text-gray-500">Ju nuk keni krijuar ende një profil biznesi.</p>
              <button 
                @click="showEditProfileModal = true" 
                class="mt-3 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Krijo Profilin
              </button>
            </div>
          </div>
        </div>
        
        <!-- Statistics -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">Statistikat</h3>
            
            <dl class="mt-5 grid grid-cols-1 gap-5 sm:grid-cols-2">
              <div class="bg-gray-50 overflow-hidden rounded-lg px-4 py-5 sm:p-6">
                <dt class="text-sm font-medium text-gray-500 truncate">Shikime Profili</dt>
                <dd class="mt-1 text-3xl font-semibold text-gray-900">{{ profileViews }}</dd>
              </div>
              
              <div class="bg-gray-50 overflow-hidden rounded-lg px-4 py-5 sm:p-6">
                <dt class="text-sm font-medium text-gray-500 truncate">Vlerësime</dt>
                <dd class="mt-1 text-3xl font-semibold text-gray-900">{{ reviewCount }}</dd>
              </div>
              
              <div class="bg-gray-50 overflow-hidden rounded-lg px-4 py-5 sm:p-6">
                <dt class="text-sm font-medium text-gray-500 truncate">Vlerësimi Mesatar</dt>
                <dd class="mt-1 text-3xl font-semibold text-gray-900">{{ averageRating || 'N/A' }}</dd>
              </div>
              
              <div class="bg-gray-50 overflow-hidden rounded-lg px-4 py-5 sm:p-6">
                <dt class="text-sm font-medium text-gray-500 truncate">Foto në Portofol</dt>
                <dd class="mt-1 text-3xl font-semibold text-gray-900">{{ portfolioCount }}</dd>
              </div>
            </dl>
          </div>
        </div>
        
        <!-- Portfolio Management -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">Portofoli</h3>
            
            <div class="mt-4">
              <button 
                @click="showAddPortfolioModal = true" 
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Shto Foto të Re
              </button>
            </div>
            
            <div v-if="portfolioImages.length > 0" class="mt-6 grid grid-cols-2 gap-4">
              <div v-for="image in portfolioImages" :key="image.id" class="relative group">
                <img :src="image.image" alt="Portfolio image" class="h-32 w-full object-cover rounded-lg">
                <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-lg flex items-center justify-center">
                  <button 
                    @click="deletePortfolioImage(image.id)" 
                    class="text-white bg-red-600 p-1 rounded-full hover:bg-red-700 focus:outline-none"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
            
            <div v-else class="mt-6 text-center py-4 border-2 border-dashed border-gray-300 rounded-lg">
              <p class="text-gray-500">Nuk keni shtuar ende foto në portofolin tuaj.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Edit Profile Modal (placeholder) -->
    <div v-if="showEditProfileModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 max-w-md w-full">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Modifiko Profilin</h3>
        <p class="text-gray-500">Ky është një modal shembull. Implementimi i plotë do të përfshinte një formular për modifikimin e profilit.</p>
        <div class="mt-5 flex justify-end">
          <button 
            @click="showEditProfileModal = false" 
            class="bg-gray-200 text-gray-800 px-4 py-2 rounded mr-2"
          >
            Mbyll
          </button>
          <button 
            class="bg-primary-600 text-white px-4 py-2 rounded"
          >
            Ruaj
          </button>
        </div>
      </div>
    </div>
    
    <!-- Add Portfolio Modal (placeholder) -->
    <div v-if="showAddPortfolioModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 max-w-md w-full">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Shto Foto në Portofol</h3>
        <p class="text-gray-500">Ky është një modal shembull. Implementimi i plotë do të përfshinte një formular për ngarkimin e fotove.</p>
        <div class="mt-5 flex justify-end">
          <button 
            @click="showAddPortfolioModal = false" 
            class="bg-gray-200 text-gray-800 px-4 py-2 rounded mr-2"
          >
            Mbyll
          </button>
          <button 
            class="bg-primary-600 text-white px-4 py-2 rounded"
          >
            Ngarko
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'

export default {
  name: 'VendorDashboard',
  setup() {
    const authStore = useAuthStore()
    
    const loading = ref(true)
    const error = ref(null)
    const vendorProfile = ref(null)
    const portfolioImages = ref([])
    const profileViews = ref(0)
    const reviewCount = ref(0)
    const averageRating = ref(null)
    const showEditProfileModal = ref(false)
    const showAddPortfolioModal = ref(false)
    
    const fetchVendorProfile = async () => {
      loading.value = true
      error.value = null
      
      try {
        // This would be replaced with actual API calls
        // For now, we'll use mock data
        setTimeout(() => {
          vendorProfile.value = {
            id: 1,
            business_name: 'Studio Fotografike Dasma Ime',
            category: { id: 1, name: 'Fotografi' },
            city: 'Tiranë',
            email: '<EMAIL>',
            phone: '+355 69 123 4567',
            description: 'Studio fotografike profesionale për dasma dhe evente të tjera.'
          }
          
          portfolioImages.value = [
            { id: 1, image: 'https://via.placeholder.com/300x200?text=Foto+1' },
            { id: 2, image: 'https://via.placeholder.com/300x200?text=Foto+2' },
            { id: 3, image: 'https://via.placeholder.com/300x200?text=Foto+3' },
            { id: 4, image: 'https://via.placeholder.com/300x200?text=Foto+4' }
          ]
          
          profileViews.value = 245
          reviewCount.value = 18
          averageRating.value = 4.7
          
          loading.value = false
        }, 1000)
      } catch (err) {
        console.error('Error fetching vendor profile:', err)
        error.value = 'Ndodhi një gabim gjatë ngarkimit të profilit. Ju lutemi provoni përsëri.'
        loading.value = false
      }
    }
    
    const deletePortfolioImage = (imageId) => {
      // This would be replaced with an actual API call
      portfolioImages.value = portfolioImages.value.filter(img => img.id !== imageId)
    }
    
    onMounted(() => {
      fetchVendorProfile()
    })
    
    return {
      loading,
      error,
      vendorProfile,
      portfolioImages,
      profileViews,
      reviewCount,
      averageRating,
      portfolioCount: () => portfolioImages.value.length,
      showEditProfileModal,
      showAddPortfolioModal,
      fetchVendorProfile,
      deletePortfolioImage
    }
  }
}
</script>
