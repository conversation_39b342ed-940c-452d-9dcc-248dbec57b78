<template>
  <div v-if="showStatus" class="api-status-banner">
    <div class="container-custom">
      <div class="flex items-center justify-between py-2">
        <div class="flex items-center">
          <i :class="statusIcon" class="mr-2"></i>
          <span class="text-sm">{{ statusMessage }}</span>
        </div>
        <button @click="hideStatus" class="text-sm hover:underline">
          Mbyll
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { isApiAvailable } from '@/utils/fallbackData'

const showStatus = ref(false)
const apiStatus = ref(null)

const statusIcon = computed(() => {
  return apiStatus.value === 'online'
    ? 'fas fa-check-circle text-green-500'
    : 'fas fa-exclamation-triangle text-yellow-500'
})

const statusMessage = computed(() => {
  return apiStatus.value === 'online'
    ? 'Të dhënat janë duke u ngarkuar nga serveri.'
    : 'Serveri nuk është i disponueshëm. Duke përdorur të dhëna shembull.'
})

const checkApiStatus = async () => {
  try {
    const available = await isApiAvailable()
    apiStatus.value = available ? 'online' : 'offline'

    // Show status only if API is offline
    if (!available) {
      showStatus.value = true
    }
  } catch (error) {
    console.warn('Error checking API status:', error)
    apiStatus.value = 'offline'
    showStatus.value = true
  }
}

const hideStatus = () => {
  showStatus.value = false
}

onMounted(() => {
  checkApiStatus()
})
</script>

<style scoped>
.api-status-banner {
  background: linear-gradient(90deg, #fef3c7 0%, #fde68a 100%);
  border-bottom: 1px solid #f59e0b;
}

.container-custom {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}
</style>
