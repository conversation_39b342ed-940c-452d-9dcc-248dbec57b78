Watching for file changes with StatReloader
Watching for file changes with StatReloader
C:\GPT4_PROJECTS\DASMAIME_AL\backend\dasmaime_project\settings.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /admin HTTP/1.1" 301 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/login/?next=/admin/ HTTP/1.1" 200 4201
"GET /static/admin/css/login.css HTTP/1.1" 200 951
Watching for file changes with StatReloader
"POST /admin/login/?next=/admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 200 15037
"GET /static/admin/css/dashboard.css HTTP/1.1" 200 441
"GET /static/admin/img/icon-changelink.svg HTTP/1.1" 200 380
"GET /admin/sites/site/ HTTP/1.1" 200 16570
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
"GET /admin/account/emailaddress/ HTTP/1.1" 200 15842
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
"GET /admin/core/vendorportfolioimage/ HTTP/1.1" 200 16431
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
"GET /admin/core/vendorcategory/ HTTP/1.1" 200 15570
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
"GET /admin/core/vendorprofile/ HTTP/1.1" 200 16973
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
"GET /admin/core/user/ HTTP/1.1" 200 19177
"GET /static/admin/img/icon-no.svg HTTP/1.1" 200 560
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
"GET /admin/core/vendorreview/ HTTP/1.1" 200 16854
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
"GET /admin/core/vendorreview/add/ HTTP/1.1" 200 21005
"GET /static/admin/css/forms.css HTTP/1.1" 200 8525
"GET /static/admin/js/prepopulate_init.js HTTP/1.1" 200 586
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
"GET /static/admin/css/widgets.css HTTP/1.1" 200 11991
"GET /static/admin/js/change_form.js HTTP/1.1" 200 606
"GET /admin/core/vendorreview/add/ HTTP/1.1" 200 21005
"GET /static/admin/img/icon-changelink.svg HTTP/1.1" 304 0
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
"GET /admin/auth/group/ HTTP/1.1" 200 14821
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
"GET /admin/core/user/ HTTP/1.1" 200 19177
"GET /static/admin/img/icon-no.svg HTTP/1.1" 304 0
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
"GET /admin/core/user/1/change/ HTTP/1.1" 200 35485
"GET /static/admin/css/forms.css HTTP/1.1" 304 0
"GET /static/admin/js/SelectBox.js HTTP/1.1" 200 4530
"GET /static/admin/js/prepopulate_init.js HTTP/1.1" 304 0
"GET /static/admin/js/SelectFilter2.js HTTP/1.1" 200 15709
"GET /static/admin/css/widgets.css HTTP/1.1" 304 0
"GET /static/admin/js/admin/DateTimeShortcuts.js HTTP/1.1" 200 19319
"GET /static/admin/js/calendar.js HTTP/1.1" 200 9141
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
"GET /static/admin/js/change_form.js HTTP/1.1" 304 0
"GET /static/admin/img/icon-clock.svg HTTP/1.1" 200 677
"GET /static/admin/img/icon-calendar.svg HTTP/1.1" 200 1086
"GET /static/admin/img/selector-icons.svg HTTP/1.1" 200 3291
"POST /admin/core/user/1/change/ HTTP/1.1" 302 0
"GET /admin/core/user/ HTTP/1.1" 200 19376
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
"GET /admin/core/user/ HTTP/1.1" 302 0
"GET /admin/login/?next=/admin/core/user/ HTTP/1.1" 200 4221
"GET /static/admin/css/login.css HTTP/1.1" 304 0
"POST /admin/login/?next=/admin/core/user/ HTTP/1.1" 302 0
"GET /admin/core/user/ HTTP/1.1" 200 19177
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
C:\GPT4_PROJECTS\DASMAIME_AL\backend\dasmaime_project\settings.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /admin/core/user/ HTTP/1.1" 200 19177
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
"GET /admin/core/user/ HTTP/1.1" 200 19177
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
"GET /admin/core/vendorprofile/ HTTP/1.1" 200 16973
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
"GET /admin/core/user/ HTTP/1.1" 200 19177
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
"GET /admin/authtoken/tokenproxy/ HTTP/1.1" 200 14461
"GET /admin/authtoken/tokenproxy/add/ HTTP/1.1" 200 16375
"GET /static/admin/css/forms.css HTTP/1.1" 304 0
"GET /static/admin/js/prepopulate_init.js HTTP/1.1" 304 0
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
"GET /static/admin/css/widgets.css HTTP/1.1" 304 0
"GET /static/admin/img/icon-changelink.svg HTTP/1.1" 304 0
"GET /static/admin/js/change_form.js HTTP/1.1" 304 0
"GET /admin/core/user/add/?_to_field=id&_popup=1 HTTP/1.1" 200 10497
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
"GET /static/admin/css/unusable_password_field.css HTTP/1.1" 200 663
"GET /static/admin/js/unusable_password_field.js HTTP/1.1" 200 1480
C:\GPT4_PROJECTS\DASMAIME_AL\backend\dasmaime_project\settings.py changed, reloading.
C:\GPT4_PROJECTS\DASMAIME_AL\backend\dasmaime_project\settings.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /admin/core/user/ HTTP/1.1" 200 19177
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
"GET /admin/authtoken/tokenproxy/ HTTP/1.1" 200 14461
"GET /admin/authtoken/tokenproxy/add/ HTTP/1.1" 200 16375
"GET /static/admin/css/forms.css HTTP/1.1" 304 0
"GET /static/admin/js/prepopulate_init.js HTTP/1.1" 304 0
"GET /static/admin/css/widgets.css HTTP/1.1" 304 0
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
"GET /static/admin/img/icon-changelink.svg HTTP/1.1" 304 0
"GET /static/admin/js/change_form.js HTTP/1.1" 304 0
"POST /admin/authtoken/tokenproxy/add/ HTTP/1.1" 302 0
"GET /admin/authtoken/tokenproxy/ HTTP/1.1" 200 15705
"GET /admin/authtoken/tokenproxy/ HTTP/1.1" 200 15476
"GET /static/admin/js/theme.js HTTP/1.1" 200 1653
"GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2810
"GET /static/admin/js/jquery.init.js HTTP/1.1" 200 347
"GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" 200 285314
"GET /static/admin/js/core.js HTTP/1.1" 200 6208
"GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 200 9777
"GET /static/admin/js/actions.js HTTP/1.1" 200 8076
"GET /static/admin/js/urlify.js HTTP/1.1" 200 7887
"GET /static/admin/js/prepopulate.js HTTP/1.1" 200 1531
"GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" 200 325171
"GET /static/admin/css/changelists.css HTTP/1.1" 200 6878
"GET /static/admin/css/responsive.css HTTP/1.1" 200 16565
"GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2808
"GET /static/admin/css/base.css HTTP/1.1" 200 22120
"GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
"GET /static/admin/js/filters.js HTTP/1.1" 200 978
"GET /static/admin/img/tooltag-add.svg HTTP/1.1" 200 331
"GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
"GET /static/admin/img/sorting-icons.svg HTTP/1.1" 200 1097
"GET /admin/core/vendorcategory/ HTTP/1.1" 200 15570
"GET /static/admin/img/search.svg HTTP/1.1" 200 458
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
"GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 200 581
"GET /admin/core/vendorprofile/ HTTP/1.1" 200 16973
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
"GET /admin/core/user/ HTTP/1.1" 200 19177
"GET /static/admin/img/icon-yes.svg HTTP/1.1" 200 436
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
"GET /admin/core/vendorreview/ HTTP/1.1" 200 16854
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
"GET /admin/socialaccount/socialaccount/ HTTP/1.1" 200 15470
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
"GET /admin/socialaccount/socialtoken/ HTTP/1.1" 200 16123
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
"GET /admin/socialaccount/socialapp/ HTTP/1.1" 200 14587
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
"GET /admin/core/vendorcategory/ HTTP/1.1" 200 15570
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
"GET /admin/core/vendorportfolioimage/ HTTP/1.1" 200 16431
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
"GET /admin/core/vendorprofile/ HTTP/1.1" 200 16973
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
"GET /admin/core/vendorprofile/ HTTP/1.1" 200 17961
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
"GET /admin/core/vendorprofile/ HTTP/1.1" 200 17961
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
"GET /admin/core/vendorcategory/ HTTP/1.1" 200 22771
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
"GET /admin/core/user/ HTTP/1.1" 200 19803
"GET /static/admin/img/icon-no.svg HTTP/1.1" 304 0
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
"GET /admin/core/vendorreview/ HTTP/1.1" 200 18018
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
"GET /admin/core/vendorportfolioimage/ HTTP/1.1" 200 17595
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
"GET /admin/core/vendorcategory/ HTTP/1.1" 200 22771
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
"GET /admin/core/vendorprofile/ HTTP/1.1" 200 24573
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
Watching for file changes with StatReloader
Not Found: /
"GET / HTTP/1.1" 404 3423
"GET /admin/ HTTP/1.1" 200 15820
"GET /static/admin/css/dashboard.css HTTP/1.1" 304 0
"GET /static/admin/img/icon-changelink.svg HTTP/1.1" 304 0
"GET /admin/core/vendorcategory/ HTTP/1.1" 200 22771
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
"GET /admin/core/vendorprofile/ HTTP/1.1" 200 34453
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
"GET /admin/core/user/ HTTP/1.1" 200 31084
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
"GET /admin/authtoken/tokenproxy/ HTTP/1.1" 200 15476
"GET /admin/account/emailaddress/ HTTP/1.1" 200 15842
"GET /admin/jsi18n/ HTTP/1.1" 200 8404
Watching for file changes with StatReloader
Watching for file changes with StatReloader
