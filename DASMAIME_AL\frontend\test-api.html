<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
</head>
<body>
    <h1>API Test</h1>
    <button onclick="testCategories()">Test Categories API</button>
    <div id="result"></div>

    <script>
        async function testCategories() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Loading...';
            
            try {
                console.log('Testing API at: http://localhost:50000/api/categories/');
                
                const response = await fetch('http://localhost:50000/api/categories/', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                console.log('Response data:', data);
                
                resultDiv.innerHTML = `
                    <h2>Success!</h2>
                    <p>Status: ${response.status}</p>
                    <p>Categories found: ${data.results ? data.results.length : data.length}</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
                
            } catch (error) {
                console.error('Error:', error);
                resultDiv.innerHTML = `
                    <h2>Error!</h2>
                    <p>${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>
