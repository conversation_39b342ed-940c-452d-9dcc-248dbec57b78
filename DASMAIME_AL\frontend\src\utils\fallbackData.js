// Fallback data për rastet kur API nuk është i disponueshëm
export const fallbackCategories = [
  {
    id: 1,
    name: 'Fotografi',
    slug: 'fotografi',
    description: 'Fotografë profesionalë për dasma dhe event',
    icon: 'fas fa-camera',
    vendor_count: 5
  },
  {
    id: 2,
    name: 'Video/Kinematografi',
    slug: 'video-kinematografi',
    description: 'Operatorë kamerash dhe montues video',
    icon: 'fas fa-video',
    vendor_count: 3
  },
  {
    id: 3,
    name: '<PERSON><PERSON><PERSON><PERSON> dhe DJ',
    slug: 'muzike-dhe-dj',
    description: 'DJ, orkestra dhe grupe muzikore',
    icon: 'fas fa-music',
    vendor_count: 4
  },
  {
    id: 4,
    name: 'Dekor<PERSON> dhe Lule',
    slug: 'dekorime-dhe-lule',
    description: 'Dekorues evento dhe floriste',
    icon: 'fas fa-seedling',
    vendor_count: 3
  },
  {
    id: 5,
    name: '<PERSON><PERSON> dhe Tortë',
    slug: 'katering-dhe-torte',
    description: 'Shërbime ushqimi dhe tortë për dasma',
    icon: 'fas fa-birthday-cake',
    vendor_count: 2
  },
  {
    id: 6,
    name: 'Fustane dhe Kostum',
    slug: 'fustane-dhe-kostum',
    description: 'Fustane nuse dhe kostume dhëndri',
    icon: 'fas fa-user-tie',
    vendor_count: 2
  },
  {
    id: 7,
    name: 'Sallone dhe Lokale',
    slug: 'sallone-dhe-lokale',
    description: 'Sallone dasme dhe lokale për event',
    icon: 'fas fa-building',
    vendor_count: 1
  },
  {
    id: 8,
    name: 'Transport',
    slug: 'transport',
    description: 'Makinë per nuse dhe transport për mysafirë',
    icon: 'fas fa-car',
    vendor_count: 1
  },
  {
    id: 9,
    name: 'Bukuri dhe Flokë',
    slug: 'bukuri-dhe-floke',
    description: 'Sallon bukurie, flokë dhe makeup artistë',
    icon: 'fas fa-cut',
    vendor_count: 2
  },
  {
    id: 10,
    name: 'Organizues Evento',
    slug: 'organizues-evento',
    description: 'Wedding planners dhe organizues profesionalë',
    icon: 'fas fa-calendar-alt',
    vendor_count: 2
  }
]

export const fallbackVendors = [
  {
    id: 1,
    business_name: 'Foto Studio AL',
    category_name: 'Fotografi',
    category_slug: 'fotografi',
    city: 'Tiranë',
    description: 'Studio profesionale fotografie me përvojë 10-vjeçare në fotografimin e dasmave.',
    services_offered: 'Fotografi dasme, portrete, event korporativ, fotografi familjare',
    min_price: 500,
    max_price: 1500,
    is_featured: true
  },
  {
    id: 2,
    business_name: 'DJ Beats Albania',
    category_name: 'Muzikë dhe DJ',
    category_slug: 'muzike-dhe-dj',
    city: 'Tiranë',
    description: 'DJ profesional me repertor të gjerë muzikor për çdo lloj eventi.',
    services_offered: 'DJ për dasma, event private, corporate events',
    min_price: 300,
    max_price: 800,
    is_featured: false
  },
  {
    id: 3,
    business_name: 'Flower Dreams',
    category_name: 'Dekorime dhe Lule',
    category_slug: 'dekorime-dhe-lule',
    city: 'Tiranë',
    description: 'Dekorime të bukura me lule të freskëta për dasma dhe event.',
    services_offered: 'Dekorime dasme, buketa nuse, centerpieces, arco floral',
    min_price: 800,
    max_price: 2500,
    is_featured: true
  },
  {
    id: 4,
    business_name: 'Royal Catering',
    category_name: 'Katering dhe Tortë',
    category_slug: 'katering-dhe-torte',
    city: 'Tiranë',
    description: 'Shërbim katering i plotë me menu të pasur dhe tortë të personalizuara.',
    services_offered: 'Katering dasme, tortë nuse, aperitiv, menu tradicional dhe ndërkombëtar',
    min_price: 2000,
    max_price: 5000,
    is_featured: false
  },
  {
    id: 5,
    business_name: 'Lens Art Photography',
    category_name: 'Fotografi',
    category_slug: 'fotografi',
    city: 'Durrës',
    description: 'Fotografe e specializuar në fotografi artistike dhe dasma.',
    services_offered: 'Fotografi dasme, engagement, maternity, newborn',
    min_price: 400,
    max_price: 1200,
    is_featured: false
  }
]

export const isApiAvailable = async () => {
  try {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 5000) // 5 second timeout

    const response = await fetch(`${import.meta.env.VITE_API_URL}/api/categories/`, {
      method: 'GET', // Changed from HEAD to GET
      signal: controller.signal,
      headers: {
        'Content-Type': 'application/json',
      }
    })

    clearTimeout(timeoutId)
    console.log('API availability check:', response.ok, response.status)
    return response.ok
  } catch (error) {
    console.warn('API not available, using fallback data:', error.message)
    return false
  }
}
