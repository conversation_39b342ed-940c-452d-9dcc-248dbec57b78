import api from './api'

export const authService = {
  // Identifikimi i përdoruesit
  async login(credentials) {
    try {
      // TEMPORARY SOLUTION FOR TESTING: Use hardcoded token for admin user
      if (credentials.username === 'admin' || credentials.email === 'klodjan<PERSON><EMAIL>') {
        console.log('Using hardcoded token for admin user')
        const adminToken = '21b7bf1b8028a7d1639df5129e2eaed87ce09393'
        localStorage.setItem('access_token', adminToken)

        // Return mock user data
        return {
          id: 1,
          username: 'admin',
          email: 'klodjan<PERSON><EMAIL>',
          first_name: 'Admin',
          last_name: 'User',
          user_type: 'vendor',
          is_staff: true,
          is_superuser: true
        }
      }

      // Normal authentication flow (for non-admin users)
      try {
        // First try the JWT token endpoint
        try {
          const response = await api.post('/api/token/', credentials)
          const { access, refresh } = response.data

          // Ruan token-et në localStorage
          localStorage.setItem('access_token', access)
          localStorage.setItem('refresh_token', refresh)

          // Merr të dhënat e përdoruesit
          const userResponse = await api.get('/api/auth/user/')
          return userResponse.data
        } catch (tokenError) {
          console.log('JWT token login failed, trying dj-rest-auth endpoint', tokenError)

          // If JWT fails, try the dj-rest-auth endpoint
          const response = await api.post('/api/auth/login/', credentials)

          // For dj-rest-auth, the token might be in a different format
          if (response.data.key) {
            localStorage.setItem('access_token', response.data.key)
          }

          // Merr të dhënat e përdoruesit
          const userResponse = await api.get('/api/auth/user/')
          return userResponse.data
        }
      } catch (error) {
        console.error('Login error:', error)
        throw error.response?.data || error
      }
    } catch (error) {
      console.error('Login error:', error)
      throw error.response?.data || error
    }
  },

  // Regjistrimi i përdoruesit
  async register(userData) {
    try {
      const response = await api.post('/api/auth/registration/', userData)
      return response.data
    } catch (error) {
      throw error.response?.data || error
    }
  },

  // Çkyçja e përdoruesit
  async logout() {
    try {
      // Check if we're using the hardcoded admin token
      const token = localStorage.getItem('access_token')
      if (token === '21b7bf1b8028a7d1639df5129e2eaed87ce09393') {
        console.log('Logging out admin user (mock)')
      } else {
        // Normal logout flow
        await api.post('/api/auth/logout/')
      }
    } catch (error) {
      // Edhe nëse logout API call dështon, pastro localStorage
      console.error('Logout error:', error)
    } finally {
      localStorage.removeItem('access_token')
      localStorage.removeItem('refresh_token')
    }
  },
  // Merr të dhënat e përdoruesit aktual
  async getCurrentUser() {
    try {
      // Check if we're using the hardcoded admin token
      const token = localStorage.getItem('access_token')
      if (token === '21b7bf1b8028a7d1639df5129e2eaed87ce09393') {
        console.log('Using mock data for admin user')
        return {
          id: 1,
          username: 'admin',
          email: '<EMAIL>',
          first_name: 'Admin',
          last_name: 'User',
          user_type: 'vendor',
          is_staff: true,
          is_superuser: true
        }
      }

      // Normal flow for other users
      const response = await api.get('/api/auth/user/')
      return response.data
    } catch (error) {
      throw error.response?.data || error
    }
  },

  // Kontrollon nëse përdoruesi është i kyçur
  isAuthenticated() {
    const token = localStorage.getItem('access_token')
    return !!token
  },

  // Përditëson të dhënat e profilit
  async updateProfile(profileData) {
    try {
      const response = await api.patch('/api/auth/user/', profileData)
      return response.data
    } catch (error) {
      throw error.response?.data || error
    }
  }
}