import { defineStore } from 'pinia'
import { vendorService } from '@/services/vendors'
import { fallbackVendors, isApiAvailable } from '@/utils/fallbackData'

export const useVendorStore = defineStore('vendors', {
  state: () => ({
    vendors: [],
    currentVendor: null,
    featuredVendors: [],
    myProfile: null,
    loading: false,
    error: null,
    pagination: {
      page: 1,
      totalPages: 1,
      totalItems: 0
    },
    filters: {
      category: '',
      city: '',
      search: '',
      minPrice: null,
      maxPrice: null
    }
  }),

  getters: {
    filteredVendors: (state) => {
      let filtered = [...state.vendors]

      if (state.filters.category) {
        filtered = filtered.filter(v => v.category_slug === state.filters.category)
      }

      if (state.filters.city) {
        filtered = filtered.filter(v =>
          v.city.toLowerCase().includes(state.filters.city.toLowerCase())
        )
      }

      if (state.filters.search) {
        const search = state.filters.search.toLowerCase()
        filtered = filtered.filter(v =>
          v.business_name.toLowerCase().includes(search) ||
          v.description.toLowerCase().includes(search)
        )
      }

      return filtered
    },
    hasProfile: (state) => !!state.myProfile
  },

  actions: {
    async fetchVendors(params = {}) {
      this.loading = true
      this.error = null

      try {
        // Check if API is available
        const apiAvailable = await isApiAvailable()

        if (apiAvailable) {
          const response = await vendorService.getVendors({
            ...params,
            page: this.pagination.page
          })

          // Handle both paginated and non-paginated responses
          const vendors = response.results || response || []
          const vendorsArray = Array.isArray(vendors) ? vendors : []

          if (params.append) {
            this.vendors.push(...vendorsArray)
          } else {
            this.vendors = vendorsArray
          }

          // Handle pagination if available
          if (response.count !== undefined) {
            this.pagination = {
              page: params.page || 1,
              totalPages: Math.ceil(response.count / 20),
              totalItems: response.count
            }
          } else {
            this.pagination = {
              page: 1,
              totalPages: 1,
              totalItems: vendors.length
            }
          }
        } else {
          // Use fallback data
          this.vendors = [...fallbackVendors]
          this.pagination = {
            page: 1,
            totalPages: 1,
            totalItems: fallbackVendors.length
          }
          console.info('Using fallback vendors data')
        }
      } catch (error) {
        // If API fails, use fallback data
        this.vendors = [...fallbackVendors]
        this.pagination = {
          page: 1,
          totalPages: 1,
          totalItems: fallbackVendors.length
        }
        this.error = null // Don't show error when using fallback
        console.warn('API failed, using fallback vendors:', error)
      } finally {
        this.loading = false
      }
    },

    async fetchVendor(id) {
      this.loading = true
      this.error = null

      try {
        const vendor = await vendorService.getVendor(id)
        this.currentVendor = vendor
        return vendor
      } catch (error) {
        this.error = error?.message || 'Gabim gjatë ngarkimit të furnitorit'
        this.currentVendor = null
        console.error('Error fetching vendor:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    async fetchFeaturedVendors() {
      try {
        const vendors = await vendorService.getFeaturedVendors()
        this.featuredVendors = vendors || []
      } catch (error) {
        this.featuredVendors = []
        console.error('Error fetching featured vendors:', error)
      }
    },

    setFilters(filters) {
      this.filters = { ...this.filters, ...filters }
      this.pagination.page = 1
    },

    clearFilters() {
      this.filters = {
        category: '',
        city: '',
        search: '',
        minPrice: null,
        maxPrice: null
      }
    }
  }
})