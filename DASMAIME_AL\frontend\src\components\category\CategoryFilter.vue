<template>
  <div class="category-filter">
    <h3 class="text-lg font-semibold mb-4">Kategoritë</h3>

    <!-- Loading State -->
    <div v-if="loading" class="py-4 flex justify-center">
      <div class="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-primary-600"></div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="py-4 text-center">
      <p class="text-red-600 text-sm mb-2">{{ error }}</p>
      <button @click="fetchCategories" class="text-primary-600 text-sm hover:underline">
        Provo përsëri
      </button>
    </div>

    <!-- Categories List -->
    <div v-else-if="Array.isArray(categories) && categories.length > 0" class="space-y-2">
      <button
        @click="selectCategory('')"
        class="w-full flex items-center py-2 px-3 rounded-md transition-colors"
        :class="selectedCategory === '' ? 'bg-primary-100 text-primary-800' : 'hover:bg-gray-100 text-gray-700'"
      >
        <i class="fas fa-th-large w-5 h-5 mr-3 text-primary-500"></i>
        <span>Të gjitha kategoritë</span>
        <span class="ml-auto text-xs font-medium bg-gray-200 text-gray-700 px-2 py-1 rounded-full">
          {{ totalVendors }}
        </span>
      </button>

      <button
        v-for="category in categories"
        :key="category?.id || Math.random()"
        @click="selectCategory(category?.slug || '')"
        class="w-full flex items-center py-2 px-3 rounded-md transition-colors"
        :class="selectedCategory === category?.slug ? 'bg-primary-100 text-primary-800' : 'hover:bg-gray-100 text-gray-700'"
      >
        <i :class="[category?.icon || 'fas fa-list', 'w-5 h-5 mr-3 text-primary-500']"></i>
        <span>{{ category?.name || 'Kategori' }}</span>
        <span class="ml-auto text-xs font-medium bg-gray-200 text-gray-700 px-2 py-1 rounded-full">
          {{ category?.vendor_count || 0 }}
        </span>
      </button>
    </div>

    <!-- No categories -->
    <div v-else class="py-4 text-center text-gray-500">
      <p class="text-sm">Nuk ka kategori të disponueshme</p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useCategoryStore } from '@/stores/categories'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  totalVendors: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits(['update:modelValue'])

const categoryStore = useCategoryStore()
const loading = ref(false)
const error = ref(null)

const categories = computed(() => categoryStore.categories)
const selectedCategory = computed(() => props.modelValue)

const fetchCategories = async () => {
  // Don't fetch if already loading or if we have categories
  if (loading.value || (Array.isArray(categories.value) && categories.value.length > 0)) return

  loading.value = true
  error.value = null

  try {
    await categoryStore.fetchCategories()
  } catch (err) {
    error.value = 'Gabim gjatë ngarkimit të kategorive'
    console.error('Error fetching categories:', err)
  } finally {
    loading.value = false
  }
}

const selectCategory = (slug) => {
  emit('update:modelValue', slug)
}

onMounted(() => {
  fetchCategories()
})
</script>

<style scoped>
/* You can add component-specific styles here */
</style>
