<template>
  <div class="categories-page">
    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-primary-600 to-primary-800 py-16 text-white">
      <div class="container-custom">
        <h1 class="text-4xl font-bold mb-4">Kategoritë e Furnitorëve</h1>
        <p class="text-xl opacity-90 max-w-2xl">
          Zgjidhni nga kategoritë tona të furnitorëve për të gjetur shërbimet më të mira për dasmën tuaj të ëndërrave.
        </p>
      </div>
    </section>

    <!-- Categories Grid -->
    <section class="py-16 bg-gray-50">
      <div class="container-custom">
        <div v-if="categoryStore.loading" class="flex justify-center items-center py-12">
          <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        </div>

        <div v-else-if="categoryStore.error" class="text-center py-12">
          <p class="text-red-600 mb-4">Ndodhi një gabim gjatë ngarkimit të kategorive.</p>
          <button
            @click="categoryStore.fetchCategories()"
            class="bg-primary-600 text-white px-4 py-2 rounded hover:bg-primary-700"
          >
            Provo përsëri
          </button>
        </div>

        <div v-else-if="!Array.isArray(categoryStore.categories) || categoryStore.categories.length === 0" class="text-center py-12">
          <p class="text-gray-600">Nuk u gjetën kategori.</p>
        </div>

        <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <CategoryCard
            v-for="category in categoryStore.categories"
            :key="category?.id || Math.random()"
            :category="category"
            size="large"
            variant="primary"
            :centered="true"
            :show-description="true"
            :show-count="true"
            @click="goToCategory(category?.slug)"
          />
        </div>
      </div>
    </section>

    <!-- Call to Action -->
    <section class="py-16 bg-white">
      <div class="container-custom text-center">
        <h2 class="text-3xl font-bold mb-6">Nuk e gjeni atë që po kërkoni?</h2>
        <p class="text-gray-600 max-w-2xl mx-auto mb-8">
          Jemi gjithmonë duke shtuar kategori të reja furnitorësh për t'ju ndihmuar të gjeni shërbimet më të mira për dasmën tuaj.
        </p>
        <router-link to="/" class="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 inline-block">
          Kthehu në faqen kryesore
        </router-link>
      </div>
    </section>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useCategoryStore } from '@/stores/categories'
import CategoryCard from '@/components/category/CategoryCard.vue'

const router = useRouter()
const categoryStore = useCategoryStore()

onMounted(async () => {
  if (categoryStore.categories.length === 0) {
    try {
      await categoryStore.fetchCategories()
    } catch (error) {
      console.error('Error loading categories:', error)
    }
  }
})

function goToCategory(slug) {
  router.push(`/category/${slug}`)
}
</script>

<style scoped>
.container-custom {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}
</style>
