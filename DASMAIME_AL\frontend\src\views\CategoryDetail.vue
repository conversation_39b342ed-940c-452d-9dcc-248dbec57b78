<template>
  <div class="category-detail-page">
    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center items-center min-h-screen">
      <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="container-custom py-16 text-center">
      <h2 class="text-2xl font-bold text-red-600 mb-4">Gabim gjatë ngarkimit të kategorisë</h2>
      <p class="mb-4">{{ error }}</p>
      <button @click="fetchCategory" class="bg-primary-600 text-white px-4 py-2 rounded hover:bg-primary-700">
        Provo përsëri
      </button>
    </div>

    <!-- Content -->
    <div v-else-if="category">
      <!-- Hero Section -->
      <section class="bg-gradient-to-r from-primary-600 to-primary-800 py-16 text-white">
        <div class="container-custom">
          <div class="flex items-center justify-center mb-6">
            <div class="w-20 h-20 rounded-full bg-white/20 flex items-center justify-center">
              <i :class="category.icon || 'fas fa-list'" class="text-4xl text-white"></i>
            </div>
          </div>
          <h1 class="text-4xl font-bold mb-4 text-center">{{ category.name }}</h1>
          <p v-if="category.description" class="text-xl opacity-90 max-w-2xl mx-auto text-center">
            {{ category.description }}
          </p>
        </div>
      </section>

      <!-- Vendors Section -->
      <section class="py-16 bg-gray-50">
        <div class="container-custom">
          <h2 class="section-title text-center">Furnitorët në {{ category.name }}</h2>

          <!-- Search and Filter -->
          <div class="mb-8 bg-white p-6 rounded-lg shadow-md">
            <div class="flex flex-col md:flex-row gap-4">
              <div class="flex-1">
                <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Kërko</label>
                <div class="relative">
                  <input
                    id="search"
                    v-model="searchQuery"
                    type="text"
                    placeholder="Kërko furnitorë..."
                    class="input-field pl-10"
                    @input="debounceSearch"
                  />
                  <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="fas fa-search text-gray-400"></i>
                  </div>
                </div>
              </div>
              <div class="w-full md:w-1/4">
                <label for="city" class="block text-sm font-medium text-gray-700 mb-1">Qyteti</label>
                <select
                  id="city"
                  v-model="cityFilter"
                  class="input-field"
                  @change="filterVendors"
                >
                  <option value="">Të gjitha qytetet</option>
                  <option v-for="city in cities" :key="city" :value="city">{{ city }}</option>
                </select>
              </div>
            </div>
          </div>

          <!-- Vendors List -->
          <div v-if="vendorStore.loading" class="flex justify-center items-center py-12">
            <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
          </div>

          <div v-else-if="vendorStore.vendors.length === 0" class="text-center py-12">
            <p class="text-gray-600 mb-4">Nuk u gjetën furnitorë në këtë kategori.</p>
            <router-link to="/categories" class="btn-primary">
              Shiko të gjitha kategoritë
            </router-link>
          </div>

          <div v-else class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div
              v-for="vendor in vendorStore.vendors"
              :key="vendor.id"
              @click="goToVendor(vendor.id)"
              class="vendor-card p-6"
            >
              <div class="flex items-center mb-3">
                <i :class="category.icon || 'fas fa-list'" class="text-xl text-primary-500 mr-3"></i>
                <h3 class="font-semibold">{{ vendor.business_name }}</h3>
              </div>
              <p class="text-gray-600 text-sm mb-2">{{ vendor.city }}</p>
              <p v-if="vendor.services_offered" class="text-gray-600 text-sm line-clamp-2">
                {{ vendor.services_offered }}
              </p>
            </div>
          </div>
        </div>
      </section>

      <!-- Related Categories -->
      <section v-if="relatedCategories.length > 0" class="py-16 bg-white">
        <div class="container-custom">
          <h2 class="section-title text-center">Kategori të Ngjashme</h2>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <router-link
              v-for="relatedCategory in relatedCategories"
              :key="relatedCategory.id"
              :to="`/category/${relatedCategory.slug}`"
              class="card p-6 text-center hover:shadow-lg transition-shadow"
            >
              <div class="flex justify-center mb-3">
                <i :class="relatedCategory.icon || 'fas fa-list'" class="text-2xl text-primary-500"></i>
              </div>
              <h3 class="font-semibold text-gray-900">{{ relatedCategory.name }}</h3>
            </router-link>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useVendorStore } from '@/stores/vendors'
import { useCategoryStore } from '@/stores/categories'
import { categoryService } from '@/services/categories'

const route = useRoute()
const router = useRouter()
const vendorStore = useVendorStore()
const categoryStore = useCategoryStore()

const category = ref(null)
const loading = ref(true)
const error = ref(null)
const searchQuery = ref('')
const cityFilter = ref('')
const searchTimeout = ref(null)

// Get unique cities from vendors
const cities = computed(() => {
  if (!vendorStore.vendors.length) return []
  const citySet = new Set(vendorStore.vendors.map(vendor => vendor.city).filter(Boolean))
  return [...citySet].sort()
})

// Get related categories (excluding current one)
const relatedCategories = computed(() => {
  if (!category.value || !categoryStore.categories.length) return []
  return categoryStore.categories
    .filter(cat => cat.id !== category.value.id)
    .slice(0, 4)
})

// Fetch category data
const fetchCategory = async () => {
  const slug = route.params.slug
  if (!slug) return

  loading.value = true
  error.value = null

  try {
    // Load all categories if not already loaded
    if (categoryStore.categories.length === 0) {
      await categoryStore.fetchCategories()
    }

    // Get category from store or API
    const foundCategory = categoryStore.getCategoryBySlug(slug)
    if (foundCategory) {
      category.value = foundCategory
    } else {
      const response = await categoryService.getCategory(slug)
      category.value = response
    }

    // Fetch vendors for this category
    await filterVendors()
  } catch (err) {
    error.value = 'Ndodhi një gabim gjatë ngarkimit të kategorisë. Ju lutemi provoni përsëri.'
    console.error('Error fetching category:', err)
  } finally {
    loading.value = false
  }
}

// Filter vendors based on category and other filters
const filterVendors = async () => {
  const params = {
    category__slug: route.params.slug
  }

  if (searchQuery.value) {
    params.search = searchQuery.value
  }

  if (cityFilter.value) {
    params.city = cityFilter.value
  }

  try {
    await vendorStore.fetchVendors(params)
  } catch (error) {
    console.error('Error filtering vendors:', error)
  }
}

// Debounce search to avoid too many API calls
const debounceSearch = () => {
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value)
  }
  
  searchTimeout.value = setTimeout(() => {
    filterVendors()
  }, 500)
}

// Navigate to vendor detail page
const goToVendor = (vendorId) => {
  router.push(`/vendors/${vendorId}`)
}

// Watch for route changes to update data
watch(() => route.params.slug, (newSlug) => {
  if (newSlug) {
    fetchCategory()
  }
})

onMounted(() => {
  fetchCategory()
})
</script>

<style scoped>
.container-custom {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
