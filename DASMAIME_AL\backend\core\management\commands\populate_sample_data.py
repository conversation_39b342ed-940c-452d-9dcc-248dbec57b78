from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from core.models import VendorCategory, VendorProfile, VendorPortfolioImage
from django.core.files.base import ContentFile
import random

User = get_user_model()


class Command(BaseCommand):
    help = 'Popullon të dhëna shembull për platformën DasmalmeAL'

    def handle(self, *args, **options):
        self.stdout.write('Duke filluar populimin e të dhënave shembull...')

        # K<PERSON>jon kategoritë e furnitorëve
        categories_data = [
            {
                'name': 'Fotografi',
                'description': 'Fotografë profesionalë për dasma dhe event',
                'icon': 'fas fa-camera',
                'order': 1
            },
            {
                'name': 'Video/Kinematografi',
                'description': 'Operatorë kamerash dhe montues video',
                'icon': 'fas fa-video',
                'order': 2
            },
            {
                'name': '<PERSON><PERSON><PERSON><PERSON> dhe DJ',
                'description': 'DJ, or<PERSON>tra dhe grupe muzikore',
                'icon': 'fas fa-music',
                'order': 3
            },
            {
                'name': 'Dekorime dhe Lule',
                'description': 'Dekorues evento dhe floriste',
                'icon': 'fas fa-seedling',
                'order': 4
            },
            {
                'name': 'Katering dhe Tortë',
                'description': 'Shërbime ushqimi dhe tortë për dasma',
                'icon': 'fas fa-birthday-cake',
                'order': 5
            },
            {
                'name': 'Fustane dhe Kostum',
                'description': 'Fustane nuse dhe kostume dhëndri',
                'icon': 'fas fa-user-tie',
                'order': 6
            },
            {
                'name': 'Sallone dhe Lokale',
                'description': 'Sallone dasme dhe lokale për event',
                'icon': 'fas fa-building',
                'order': 7
            },
            {
                'name': 'Transport',
                'description': 'Makinë per nuse dhe transport për mysafirë',
                'icon': 'fas fa-car',
                'order': 8
            },
            {
                'name': 'Bukuri dhe Flokë',
                'description': 'Sallon bukurie, flokë dhe makeup artistë',
                'icon': 'fas fa-cut',
                'order': 9
            },
            {
                'name': 'Organizues Evento',
                'description': 'Wedding planners dhe organizues profesionalë',
                'icon': 'fas fa-calendar-alt',
                'order': 10
            },
        ]

        # Krijon kategoritë
        created_categories = 0
        for cat_data in categories_data:
            category, created = VendorCategory.objects.get_or_create(
                name=cat_data['name'],
                defaults=cat_data
            )
            if created:
                created_categories += 1
                self.stdout.write(
                    self.style.SUCCESS(f'✓ Kategoria "{category.name}" u krijua')
                )

        # Krijon përdorues dhe furnitorë shembull
        vendors_data = [
            # Fotografë
            {
                'username': 'foto_studio_al',
                'email': '<EMAIL>',
                'first_name': 'Arben',
                'last_name': 'Krasniqi',
                'business_name': 'Foto Studio AL',
                'category': 'Fotografi',
                'city': 'Tiranë',
                'address': 'Rruga e Kavajës, Tiranë',
                'phone': '+355692123456',
                'description': 'Studio profesionale fotografie me përvojë 10-vjeçare në fotografimin e dasmave.',
                'services_offered': 'Fotografi dasme, portrete, event korporativ, fotografi familjare',
                'price_range': '500-1500',
                'website': 'https://fotostudioal.com'
            },
            {
                'username': 'lens_art_photo',
                'email': '<EMAIL>',
                'first_name': 'Elona',
                'last_name': 'Hoxha',
                'business_name': 'Lens Art Photography',
                'category': 'Fotografi',
                'city': 'Durrës',
                'address': 'Rruga Durrësit, Durrës',
                'phone': '+355693234567',
                'description': 'Fotografe e specializuar në fotografi artistike dhe dasma.',
                'services_offered': 'Fotografi dasme, engagement, maternity, newborn',
                'price_range': '400-1200',
                'website': 'https://lensart.al'
            },
            # DJ dhe Muzikë
            {
                'username': 'dj_beats_al',
                'email': '<EMAIL>',
                'first_name': 'Ermal',
                'last_name': 'Shehu',
                'business_name': 'DJ Beats Albania',
                'category': 'Muzikë dhe DJ',
                'city': 'Tiranë',
                'address': 'Blloku, Tiranë',
                'phone': '+355694345678',
                'description': 'DJ profesional me repertor të gjerë muzikor për çdo lloj eventi.',
                'services_offered': 'DJ për dasma, event private, corporate events',
                'price_range': '300-800',
                'website': 'https://djbeats.al'
            },
            # Dekorime
            {
                'username': 'flower_dreams',
                'email': '<EMAIL>',
                'first_name': 'Manjola',
                'last_name': 'Tafa',
                'business_name': 'Flower Dreams',
                'category': 'Dekorime dhe Lule',
                'city': 'Tiranë',
                'address': 'Rruga Mine Peza, Tiranë',
                'phone': '+355695456789',
                'description': 'Dekorime të bukura me lule të freskëta për dasma dhe event.',
                'services_offered': 'Dekorime dasme, buketa nuse, centerpieces, arco floral',
                'price_range': '800-2500',
                'website': 'https://flowerdreams.al'
            },
            # Katering
            {
                'username': 'royal_catering',
                'email': '<EMAIL>',
                'first_name': 'Genti',
                'last_name': 'Rama',
                'business_name': 'Royal Catering',
                'category': 'Katering dhe Tortë',
                'city': 'Tiranë',
                'address': 'Rruga e Elbasanit, Tiranë',
                'phone': '+355696567890',
                'description': 'Shërbim katering i plotë me menu të pasur dhe tortë të personalizuara.',
                'services_offered': 'Katering dasme, tortë nuse, aperitiv, menu tradicional dhe ndërkombëtar',
                'price_range': '2000-5000',
                'website': 'https://royalcatering.al'
            },
            # Video/Kinematografi
            {
                'username': 'cinematic_dreams',
                'email': '<EMAIL>',
                'first_name': 'Klajdi',
                'last_name': 'Mema',
                'business_name': 'Cinematic Dreams',
                'category': 'Video/Kinematografi',
                'city': 'Tiranë',
                'address': 'Rruga e Durrësit, Tiranë',
                'phone': '+355697678901',
                'description': 'Produksion video profesional për dasma me teknologji të avancuar.',
                'services_offered': 'Video dasme, drone footage, same day edit, highlight reel',
                'price_range': '800-2000',
                'website': 'https://cinematicdreams.al'
            },
            # Fustane dhe Kostum
            {
                'username': 'bridal_elegance',
                'email': '<EMAIL>',
                'first_name': 'Silvana',
                'last_name': 'Koci',
                'business_name': 'Bridal Elegance',
                'category': 'Fustane dhe Kostum',
                'city': 'Tiranë',
                'address': 'Rruga e Kavajës, Tiranë',
                'phone': '+355698789012',
                'description': 'Salon i specializuar për fustane nuse dhe kostume dhëndri.',
                'services_offered': 'Fustane nuse, kostume dhëndri, aksesorë, alterime',
                'price_range': '600-2500',
                'website': 'https://bridalelegance.al'
            },
            # Sallone dhe Lokale
            {
                'username': 'grand_ballroom',
                'email': '<EMAIL>',
                'first_name': 'Agron',
                'last_name': 'Duka',
                'business_name': 'Grand Ballroom',
                'category': 'Sallone dhe Lokale',
                'city': 'Tiranë',
                'address': 'Rruga e Elbasanit, Tiranë',
                'phone': '+355699890123',
                'description': 'Sallone elegante për dasma dhe event me kapacitet deri në 500 persona.',
                'services_offered': 'Sallone dasme, event korporativ, organizim i plotë',
                'price_range': '3000-8000',
                'website': 'https://grandballroom.al'
            },
            # Transport
            {
                'username': 'luxury_cars_al',
                'email': '<EMAIL>',
                'first_name': 'Besnik',
                'last_name': 'Hoxha',
                'business_name': 'Luxury Cars Albania',
                'category': 'Transport',
                'city': 'Tiranë',
                'address': 'Blloku, Tiranë',
                'phone': '+355690901234',
                'description': 'Makinë luksoze për nuse dhe transport VIP për mysafirë.',
                'services_offered': 'Makinë nuse, transport mysafirë, limousine, vintage cars',
                'price_range': '200-800',
                'website': 'https://luxurycars.al'
            },
            # Bukuri dhe Flokë
            {
                'username': 'beauty_studio_al',
                'email': '<EMAIL>',
                'first_name': 'Anxhela',
                'last_name': 'Pepa',
                'business_name': 'Beauty Studio AL',
                'category': 'Bukuri dhe Flokë',
                'city': 'Tiranë',
                'address': 'Rruga e Kavajës, Tiranë',
                'phone': '+355691012345',
                'description': 'Studio bukurie i specializuar për nuse me shërbime të plota.',
                'services_offered': 'Makeup nuse, flokë, manikyr, pedikyr, spa treatments',
                'price_range': '150-500',
                'website': 'https://beautystudio.al'
            },
            # Organizues Evento
            {
                'username': 'perfect_weddings',
                'email': '<EMAIL>',
                'first_name': 'Erjona',
                'last_name': 'Sulaj',
                'business_name': 'Perfect Weddings',
                'category': 'Organizues Evento',
                'city': 'Tiranë',
                'address': 'Blloku, Tiranë',
                'phone': '+355692123456',
                'description': 'Wedding planner me përvojë për organizimin e dasmave të përkryera.',
                'services_offered': 'Wedding planning, koordinim eventi, dekorime, vendor management',
                'price_range': '1000-3000',
                'website': 'https://perfectweddings.al'
            }
        ]

        created_vendors = 0
        for vendor_data in vendors_data:
            # Krijon përdoruesin
            user, user_created = User.objects.get_or_create(
                username=vendor_data['username'],
                defaults={
                    'email': vendor_data['email'],
                    'first_name': vendor_data['first_name'],
                    'last_name': vendor_data['last_name'],
                    'user_type': 'vendor',
                    'phone_number': vendor_data['phone'],
                    'is_verified': True
                }
            )

            if user_created:
                user.set_password('password123')
                user.save()

            # Gjen kategorinë
            try:
                category = VendorCategory.objects.get(name=vendor_data['category'])
            except VendorCategory.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'Kategoria "{vendor_data["category"]}" nuk u gjet')
                )
                continue

            # Krijon profilin e furnitorit
            vendor_profile, profile_created = VendorProfile.objects.get_or_create(
                user=user,
                defaults={
                    'business_name': vendor_data['business_name'],
                    'category': category,
                    'city': vendor_data['city'],
                    'address': vendor_data['address'],
                    'description': vendor_data['description'],
                    'services_offered': vendor_data['services_offered'],
                    'contact_email': vendor_data['email'],
                    'contact_phone': vendor_data['phone'],
                    'website_url': vendor_data['website'],
                    'min_price': int(vendor_data['price_range'].split('-')[0]) if '-' in vendor_data['price_range'] else None,
                    'max_price': int(vendor_data['price_range'].split('-')[1]) if '-' in vendor_data['price_range'] else None,
                    'years_of_experience': random.randint(2, 15),
                    'approval_status': 'approved',
                    'is_featured': random.choice([True, False])
                }
            )

            if profile_created:
                created_vendors += 1
                self.stdout.write(
                    self.style.SUCCESS(f'✓ Furnitori "{vendor_profile.business_name}" u krijua')
                )

        self.stdout.write(
            self.style.SUCCESS(
                f'\nU përfundua! {created_categories} kategori dhe {created_vendors} furnitorë të rinj u krijuan.'
            )
        )

        self.stdout.write(
            self.style.SUCCESS(
                '\nTë dhënat e krijuara:'
            )
        )
        self.stdout.write(f'- Kategori: {VendorCategory.objects.count()}')
        self.stdout.write(f'- Furnitorë: {VendorProfile.objects.count()}')
        self.stdout.write(f'- Përdorues: {User.objects.count()}')
