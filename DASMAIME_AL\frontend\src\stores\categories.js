import { defineStore } from 'pinia'
import { categoryService } from '@/services/categories'
import { fallbackCategories, isApiAvailable } from '@/utils/fallbackData'

export const useCategoryStore = defineStore('categories', {
  state: () => ({
    categories: [],
    loading: false,
    error: null,
    currentCategory: null
  }),

  getters: {
    getCategoryBySlug: (state) => (slug) => {
      return state.categories.find(cat => cat.slug === slug)
    },

    activeCategoriesCount: (state) => state.categories.length,

    // Get categories sorted by vendor count
    popularCategories: (state) => {
      return [...state.categories]
        .sort((a, b) => (b.vendor_count || 0) - (a.vendor_count || 0))
    },

    // Get categories with at least one vendor
    activeCategories: (state) => {
      return state.categories.filter(cat => cat.vendor_count > 0)
    }
  },

  actions: {
    async fetchCategories() {
      if (this.categories.length > 0) return // Cache if already loaded

      this.loading = true
      this.error = null

      try {
        console.log('Fetching categories from API...')
        const response = await categoryService.getCategories()
        console.log('API response:', response)

        // Handle both paginated and non-paginated responses
        const categories = response.results || response || []
        this.categories = Array.isArray(categories) ? categories : []

        console.log('Categories loaded:', this.categories.length)
      } catch (error) {
        console.warn('API failed, using fallback categories:', error)
        // If API fails, use fallback data
        this.categories = [...fallbackCategories]
        this.error = null // Don't show error when using fallback
      } finally {
        this.loading = false
      }
    },

    async fetchCategoryBySlug(slug) {
      this.loading = true
      this.error = null

      try {
        // First check if we already have it in the store
        const existingCategory = this.getCategoryBySlug(slug)
        if (existingCategory) {
          this.currentCategory = existingCategory
          return existingCategory
        }

        // If not, fetch it from the API
        const category = await categoryService.getCategory(slug)
        this.currentCategory = category
        return category
      } catch (error) {
        this.error = error?.message || 'Gabim gjatë ngarkimit të kategorisë'
        this.currentCategory = null
        console.error('Error fetching category:', error)
        throw error
      } finally {
        this.loading = false
      }
    }
  }
})