import api from './api'

export const categoryService = {
  // Merr listen e kategorive
  async getCategories() {
    try {
      const response = await api.get('/api/categories/')
      return response.data
    } catch (error) {
      console.error('Error fetching categories:', error)
      throw error.response?.data || error
    }
  },

  // Merr një kategori sipas slug
  async getCategory(slug) {
    try {
      const response = await api.get(`/api/categories/${slug}/`)
      return response.data
    } catch (error) {
      console.error('Error fetching category:', error)
      throw error.response?.data || error
    }
  }
}