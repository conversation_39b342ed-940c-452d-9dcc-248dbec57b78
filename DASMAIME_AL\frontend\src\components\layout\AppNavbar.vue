<template>
  <nav class="bg-gradient-to-r from-white via-primary-50 to-secondary-50 shadow-lg sticky top-0 z-50 border-b border-primary-100">
    <div class="container-custom">
      <div class="flex justify-between items-center h-16">
        <!-- Logo -->
        <router-link to="/" class="flex items-center space-x-3">
          <div class="text-2xl font-heading font-bold bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent">
            DasmalmeAL
          </div>
        </router-link>

        <!-- Desktop Navigation -->
        <div class="hidden md:flex items-center space-x-8">
          <router-link
            to="/"
            class="text-gray-700 hover:text-primary-600 font-medium transition-colors"
          >
            Faqja <PERSON>
          </router-link>

          <div class="relative" @mouseleave="showCategoriesDropdown = false">
            <button
              @mouseenter="showCategoriesDropdown = true"
              class="text-gray-700 hover:text-primary-600 font-medium transition-colors flex items-center"
            >
              Kategoritë
              <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>

            <!-- Categories Dropdown -->
            <div
              v-show="showCategoriesDropdown"
              @mouseenter="showCategoriesDropdown = true"
              class="absolute top-full left-0 mt-1 w-64 bg-white rounded-lg shadow-xl border border-gray-200 py-2 z-50"
            >
              <router-link
                to="/categories"
                class="flex items-center px-4 py-2 hover:bg-gray-50 text-gray-700 hover:text-primary-600 font-semibold border-b border-gray-100"
              >
                <i class="fas fa-th-large w-5 h-5 mr-3 text-primary-500"></i>
                Të gjitha kategoritë
              </router-link>

              <router-link
                v-for="category in categories"
                v-if="Array.isArray(categories) && categories.length > 0"
                :key="category?.id || Math.random()"
                :to="`/category/${category?.slug || ''}`"
                class="flex items-center px-4 py-2 hover:bg-gray-50 text-gray-700 hover:text-primary-600"
              >
                <i :class="category?.icon || 'fas fa-list'" class="w-5 h-5 mr-3 text-primary-500"></i>
                {{ category?.name || 'Kategori' }}
              </router-link>
            </div>
          </div>

          <!-- Auth Section -->
          <div v-if="!authStore.isAuthenticated" class="flex items-center space-x-4">
            <router-link
              to="/login"
              class="text-gray-700 hover:text-primary-600 font-medium transition-colors"
            >
              Identifikimi
            </router-link>
            <router-link
              to="/register"
              class="btn-primary"
            >
              Regjistrimi
            </router-link>
          </div>

          <!-- User Menu (when authenticated) -->
          <div v-else class="relative">
            <button
              @click="showUserMenu = !showUserMenu"
              class="flex items-center space-x-2 text-gray-700 hover:text-primary-600"
            >
              <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                <span class="text-primary-600 font-semibold text-sm">
                  {{ authStore.user?.first_name?.charAt(0) || authStore.user?.username?.charAt(0) }}
                </span>
              </div>
              <span class="font-medium">{{ authStore.user?.first_name || authStore.user?.username }}</span>
            </button>

            <!-- User Dropdown -->
            <div
              v-show="showUserMenu"
              @click.away="showUserMenu = false"
              class="absolute top-full right-0 mt-1 w-48 bg-white rounded-lg shadow-xl border border-gray-200 py-2 z-50"
            >
              <router-link
                v-if="authStore.isVendor"
                to="/vendor"
                class="block px-4 py-2 hover:bg-gray-50 text-gray-700 hover:text-primary-600"
              >
                Paneli i Furnitorit
              </router-link>

              <button
                @click="handleLogout"
                class="block w-full px-4 py-2 text-left hover:bg-gray-50 text-red-600 hover:text-red-700"
              >
                Dil nga llogaria
              </button>
            </div>
          </div>
        </div>

        <!-- Mobile menu button -->
        <button
          @click="showMobileMenu = !showMobileMenu"
          class="md:hidden p-2 rounded-md text-gray-700 hover:text-primary-600"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              v-if="!showMobileMenu"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4 6h16M4 12h16M4 18h16"
            ></path>
            <path
              v-else
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            ></path>
          </svg>
        </button>
      </div>

      <!-- Mobile Navigation -->
      <div v-show="showMobileMenu" class="md:hidden py-4 border-t border-gray-200">
        <div class="space-y-2">
          <router-link
            to="/"
            @click="showMobileMenu = false"
            class="block px-4 py-2 text-gray-700 hover:text-primary-600 font-medium"
          >
            Faqja Kryesore
          </router-link>

          <div class="px-4 py-2">
            <div class="text-sm font-semibold text-gray-500 mb-2">Kategoritë</div>
            <div class="space-y-1 ml-4">
              <router-link
                to="/categories"
                @click="showMobileMenu = false"
                class="flex items-center py-1 text-gray-600 hover:text-primary-600 font-semibold"
              >
                <i class="fas fa-th-large w-4 h-4 mr-2 text-primary-500"></i>
                Të gjitha kategoritë
              </router-link>

              <router-link
                v-for="category in categories"
                v-if="Array.isArray(categories) && categories.length > 0"
                :key="category?.id || Math.random()"
                :to="`/category/${category?.slug || ''}`"
                @click="showMobileMenu = false"
                class="flex items-center py-1 text-gray-600 hover:text-primary-600"
              >
                <i :class="category?.icon || 'fas fa-list'" class="w-4 h-4 mr-2 text-primary-500"></i>
                {{ category?.name || 'Kategori' }}
              </router-link>
            </div>
          </div>

          <div v-if="!authStore.isAuthenticated" class="px-4 py-2 space-y-2">
            <router-link
              to="/login"
              @click="showMobileMenu = false"
              class="block text-gray-700 hover:text-primary-600 font-medium"
            >
              Identifikimi
            </router-link>
            <router-link
              to="/register"
              @click="showMobileMenu = false"
              class="block btn-primary text-center"
            >
              Regjistrimi
            </router-link>
          </div>

          <div v-else class="px-4 py-2 space-y-2">
            <div class="flex items-center space-x-2 py-2">
              <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                <span class="text-primary-600 font-semibold text-sm">
                  {{ authStore.user?.first_name?.charAt(0) || authStore.user?.username?.charAt(0) }}
                </span>
              </div>
              <span class="font-medium">{{ authStore.user?.first_name || authStore.user?.username }}</span>
            </div>

            <router-link
              v-if="authStore.isVendor"
              to="/vendor"
              @click="showMobileMenu = false"
              class="block text-gray-700 hover:text-primary-600 font-medium"
            >
              Paneli i Furnitorit
            </router-link>

            <button
              @click="handleLogout"
              class="block w-full text-left text-red-600 hover:text-red-700 font-medium"
            >
              Dil nga llogaria
            </button>
          </div>
        </div>
      </div>
    </div>
  </nav>
</template>
<script>
import { ref, onMounted, computed } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useVendorStore } from '@/stores/vendors'
import { useCategoryStore } from '@/stores/categories'
import { useRouter } from 'vue-router'

export default {
  name: 'AppNavbar',
  setup() {
    const authStore = useAuthStore()
    const vendorStore = useVendorStore()
    const categoryStore = useCategoryStore()
    const router = useRouter()

    const showMobileMenu = ref(false)
    const showUserMenu = ref(false)
    const showCategoriesDropdown = ref(false)

    // Use computed to get categories from store
    const categories = computed(() => {
      return Array.isArray(categoryStore.categories) ? categoryStore.categories : []
    })

    const handleLogout = async () => {
      await authStore.logout()
      showUserMenu.value = false
      showMobileMenu.value = false
      router.push('/')
    }

    const fetchCategories = async () => {
      try {
        await categoryStore.fetchCategories()
      } catch (error) {
        console.error('Error fetching categories:', error)
      }
    }

    onMounted(() => {
      fetchCategories()
    })

    return {
      authStore,
      showMobileMenu,
      showUserMenu,
      showCategoriesDropdown,
      categories,
      handleLogout
    }
  }
}
</script>

<style scoped>
/* Additional component-specific styles can be added here */
</style>