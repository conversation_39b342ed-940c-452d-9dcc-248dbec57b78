<template>
  <div 
    class="category-card bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow cursor-pointer"
    :class="{ 'text-center': centered }"
    @click="$emit('click')"
  >
    <div class="p-6" :class="{ 'flex flex-col items-center': centered }">
      <!-- Category Icon -->
      <div 
        class="category-icon rounded-full flex items-center justify-center mb-4"
        :class="[
          size === 'small' ? 'w-12 h-12' : size === 'large' ? 'w-20 h-20' : 'w-16 h-16',
          centered ? 'mx-auto' : '',
          variant === 'primary' ? 'bg-primary-100' : 'bg-gray-100'
        ]"
      >
        <i 
          :class="[
            category.icon || 'fas fa-list', 
            size === 'small' ? 'text-xl' : size === 'large' ? 'text-3xl' : 'text-2xl',
            variant === 'primary' ? 'text-primary-600' : 'text-gray-700'
          ]"
        ></i>
      </div>
      
      <!-- Category Name -->
      <h3 
        class="font-semibold text-gray-900 mb-2"
        :class="[
          size === 'small' ? 'text-base' : size === 'large' ? 'text-2xl' : 'text-xl'
        ]"
      >
        {{ category.name }}
      </h3>
      
      <!-- Category Description (optional) -->
      <p 
        v-if="showDescription && category.description" 
        class="text-gray-600 mb-4"
        :class="[
          size === 'small' ? 'text-xs' : size === 'large' ? 'text-base' : 'text-sm'
        ]"
      >
        {{ category.description }}
      </p>
      
      <!-- Vendor Count (optional) -->
      <p 
        v-if="showCount" 
        class="text-sm font-medium"
        :class="[
          variant === 'primary' ? 'text-primary-600' : 'text-gray-600'
        ]"
      >
        {{ category.vendor_count || 0 }} furnitorë
      </p>
    </div>
  </div>
</template>

<script setup>
defineProps({
  category: {
    type: Object,
    required: true
  },
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  variant: {
    type: String,
    default: 'primary',
    validator: (value) => ['primary', 'neutral'].includes(value)
  },
  centered: {
    type: Boolean,
    default: true
  },
  showDescription: {
    type: Boolean,
    default: true
  },
  showCount: {
    type: Boolean,
    default: true
  }
})

defineEmits(['click'])
</script>

<style scoped>
/* You can add component-specific styles here */
</style>
