from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from core.models import VendorCategory, VendorProfile
import random

User = get_user_model()


class Command(BaseCommand):
    help = 'Shton më shumë furnitorë në qytete të ndryshme'

    def handle(self, *args, **options):
        self.stdout.write('<PERSON> shtuar furnitorë të rinj në qytete të ndryshme...')
        
        # Furnitorë të rinj në qytete të ndryshme
        vendors_data = [
            # Durrës
            {
                'username': 'durres_photo',
                'email': '<EMAIL>',
                'first_name': 'Alba<PERSON>',
                'last_name': '<PERSON><PERSON><PERSON>',
                'business_name': 'Durrës Photography',
                'category': 'Fotografi',
                'city': 'Durrës',
                'address': '<PERSON><PERSON><PERSON>, Durrës',
                'phone': '+355693111222',
                'description': 'Studio fotografik në Durrës me shërbime profesionale për dasma.',
                'services_offered': 'Fotografi dasme, portrete, event, fotografi plazhi',
                'price_range': '350-1000',
                'website': 'https://durresphoto.al'
            },
            {
                'username': 'seaside_events',
                'email': '<EMAIL>',
                'first_name': 'Mirela',
                'last_name': 'Topi',
                'business_name': 'Seaside Events',
                'category': 'Organizues Evento',
                'city': 'Durrës',
                'address': 'Plazhi i Durrësit, Durrës',
                'phone': '+355694222333',
                'description': 'Organizues evento të specializuar për dasma në plazh.',
                'services_offered': 'Beach weddings, event planning, dekorime plazhi',
                'price_range': '800-2500',
                'website': 'https://seasideevents.al'
            },
            # Vlorë
            {
                'username': 'vlora_music',
                'email': '<EMAIL>',
                'first_name': 'Endrit',
                'last_name': 'Hyseni',
                'business_name': 'Vlorë Music Group',
                'category': 'Muzikë dhe DJ',
                'city': 'Vlorë',
                'address': 'Rruga Ismail Qemali, Vlorë',
                'phone': '+355695333444',
                'description': 'Grup muzikor dhe DJ për dasma në Vlorë dhe rrethinë.',
                'services_offered': 'Live music, DJ, orkestër, muzikë tradicionale',
                'price_range': '400-1200',
                'website': 'https://vloramusic.al'
            },
            {
                'username': 'riviera_catering',
                'email': '<EMAIL>',
                'first_name': 'Fatmir',
                'last_name': 'Kola',
                'business_name': 'Riviera Catering',
                'category': 'Katering dhe Tortë',
                'city': 'Vlorë',
                'address': 'Lungomare, Vlorë',
                'phone': '+355696444555',
                'description': 'Katering me specialitete deti për dasma në Rivierën Shqiptare.',
                'services_offered': 'Seafood catering, tortë nuse, aperitiv, menu deti',
                'price_range': '1800-4500',
                'website': 'https://rivieracatering.al'
            },
            # Shkodër
            {
                'username': 'shkoder_video',
                'email': '<EMAIL>',
                'first_name': 'Ardit',
                'last_name': 'Marku',
                'business_name': 'Shkodër Video Production',
                'category': 'Video/Kinematografi',
                'city': 'Shkodër',
                'address': 'Rruga Kol Idromeno, Shkodër',
                'phone': '+355697555666',
                'description': 'Produksion video profesional në Shkodër dhe Alpet Shqiptare.',
                'services_offered': 'Video dasme, drone, cinematic videos, nature shots',
                'price_range': '600-1800',
                'website': 'https://shkodervideo.al'
            },
            {
                'username': 'alpine_flowers',
                'email': '<EMAIL>',
                'first_name': 'Lindita',
                'last_name': 'Bushati',
                'business_name': 'Alpine Flowers',
                'category': 'Dekorime dhe Lule',
                'city': 'Shkodër',
                'address': 'Rruga Marin Barleti, Shkodër',
                'phone': '+355698666777',
                'description': 'Dekorime me lule alpine dhe tradicionale për dasma në Shkodër.',
                'services_offered': 'Dekorime dasme, buketa alpine, centerpieces, lule tradicionale',
                'price_range': '600-2000',
                'website': 'https://alpineflowers.al'
            },
            # Korçë
            {
                'username': 'korce_elegance',
                'email': '<EMAIL>',
                'first_name': 'Gentiana',
                'last_name': 'Nano',
                'business_name': 'Korçë Elegance',
                'category': 'Fustane dhe Kostum',
                'city': 'Korçë',
                'address': 'Bulevardi Republika, Korçë',
                'phone': '+355699777888',
                'description': 'Atelier i specializuar për fustane nuse dhe kostume në Korçë.',
                'services_offered': 'Fustane nuse custom, kostume dhëndri, aksesorë, qiramarrje',
                'price_range': '500-2000',
                'website': 'https://korceelegance.al'
            },
            {
                'username': 'mountain_beauty',
                'email': '<EMAIL>',
                'first_name': 'Elona',
                'last_name': 'Dilo',
                'business_name': 'Mountain Beauty Salon',
                'category': 'Bukuri dhe Flokë',
                'city': 'Korçë',
                'address': 'Rruga Fan Noli, Korçë',
                'phone': '+355690888999',
                'description': 'Salon bukurie modern në Korçë me shërbime të plota për nuse.',
                'services_offered': 'Makeup nuse, styling flokësh, manikyr, pedikyr, facial',
                'price_range': '120-400',
                'website': 'https://mountainbeauty.al'
            }
        ]
        
        created_vendors = 0
        for vendor_data in vendors_data:
            # Krijon përdoruesin
            user, user_created = User.objects.get_or_create(
                username=vendor_data['username'],
                defaults={
                    'email': vendor_data['email'],
                    'first_name': vendor_data['first_name'],
                    'last_name': vendor_data['last_name'],
                    'user_type': 'vendor',
                    'phone_number': vendor_data['phone'],
                    'is_verified': True
                }
            )
            
            if user_created:
                user.set_password('password123')
                user.save()
            
            # Gjen kategorinë
            try:
                category = VendorCategory.objects.get(name=vendor_data['category'])
            except VendorCategory.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'Kategoria "{vendor_data["category"]}" nuk u gjet')
                )
                continue
            
            # Krijon profilin e furnitorit
            vendor_profile, profile_created = VendorProfile.objects.get_or_create(
                user=user,
                defaults={
                    'business_name': vendor_data['business_name'],
                    'category': category,
                    'city': vendor_data['city'],
                    'address': vendor_data['address'],
                    'description': vendor_data['description'],
                    'services_offered': vendor_data['services_offered'],
                    'contact_email': vendor_data['email'],
                    'contact_phone': vendor_data['phone'],
                    'website_url': vendor_data['website'],
                    'min_price': int(vendor_data['price_range'].split('-')[0]) if '-' in vendor_data['price_range'] else None,
                    'max_price': int(vendor_data['price_range'].split('-')[1]) if '-' in vendor_data['price_range'] else None,
                    'years_of_experience': random.randint(3, 12),
                    'approval_status': 'approved',
                    'is_featured': random.choice([True, False])
                }
            )
            
            if profile_created:
                created_vendors += 1
                self.stdout.write(
                    self.style.SUCCESS(f'✓ Furnitori "{vendor_profile.business_name}" u krijua në {vendor_profile.city}')
                )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'\nU përfundua! {created_vendors} furnitorë të rinj u shtuan.'
            )
        )
        
        self.stdout.write(
            self.style.SUCCESS(
                '\nStatistika e përgjithshme:'
            )
        )
        self.stdout.write(f'- Kategori: {VendorCategory.objects.count()}')
        self.stdout.write(f'- Furnitorë: {VendorProfile.objects.count()}')
        self.stdout.write(f'- Përdorues: {User.objects.count()}')
        
        # Shfaq furnitorët sipas qyteteve
        cities = VendorProfile.objects.values_list('city', flat=True).distinct()
        self.stdout.write('\nFurnitorë sipas qyteteve:')
        for city in cities:
            count = VendorProfile.objects.filter(city=city).count()
            self.stdout.write(f'- {city}: {count} furnitorë')
