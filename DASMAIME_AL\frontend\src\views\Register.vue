<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Regjistrohuni për një llogari të re
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          Ose
          <router-link to="/login" class="font-medium text-primary-600 hover:text-primary-500">
            identifikohuni nëse keni një llogari
          </router-link>
        </p>
      </div>
      
      <div v-if="error" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert">
        <span class="block sm:inline">{{ error }}</span>
      </div>
      
      <form class="mt-8 space-y-6" @submit.prevent="handleRegister">
        <div class="rounded-md shadow-sm -space-y-px">
          <div>
            <label for="user-type" class="sr-only">Lloji i përdoruesit</label>
            <select 
              id="user-type" 
              name="user-type" 
              required 
              v-model="userType"
              class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
            >
              <option value="" disabled>Zgjidhni llojin e përdoruesit</option>
              <option value="couple">Çift</option>
              <option value="vendor">Furnitor</option>
            </select>
          </div>
          
          <div v-if="userType === 'vendor'">
            <label for="business-name" class="sr-only">Emri i biznesit</label>
            <input 
              id="business-name" 
              name="business-name" 
              type="text" 
              required 
              v-model="businessName"
              class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm" 
              placeholder="Emri i biznesit"
            >
          </div>
          
          <div>
            <label for="first-name" class="sr-only">Emri</label>
            <input 
              id="first-name" 
              name="first-name" 
              type="text" 
              required 
              v-model="firstName"
              class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm" 
              placeholder="Emri"
            >
          </div>
          
          <div>
            <label for="last-name" class="sr-only">Mbiemri</label>
            <input 
              id="last-name" 
              name="last-name" 
              type="text" 
              required 
              v-model="lastName"
              class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm" 
              placeholder="Mbiemri"
            >
          </div>
          
          <div>
            <label for="email" class="sr-only">Email</label>
            <input 
              id="email" 
              name="email" 
              type="email" 
              autocomplete="email" 
              required 
              v-model="email"
              class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm" 
              placeholder="Adresa e emailit"
            >
          </div>
          
          <div>
            <label for="password1" class="sr-only">Fjalëkalimi</label>
            <input 
              id="password1" 
              name="password1" 
              type="password" 
              required 
              v-model="password1"
              class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm" 
              placeholder="Fjalëkalimi"
            >
          </div>
          
          <div>
            <label for="password2" class="sr-only">Konfirmo fjalëkalimin</label>
            <input 
              id="password2" 
              name="password2" 
              type="password" 
              required 
              v-model="password2"
              class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm" 
              placeholder="Konfirmo fjalëkalimin"
            >
          </div>
        </div>

        <div class="flex items-center">
          <input 
            id="terms" 
            name="terms" 
            type="checkbox" 
            required
            v-model="agreeToTerms"
            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
          >
          <label for="terms" class="ml-2 block text-sm text-gray-900">
            Unë pranoj <a href="#" class="text-primary-600 hover:text-primary-500">kushtet e përdorimit</a> dhe <a href="#" class="text-primary-600 hover:text-primary-500">politikën e privatësisë</a>
          </label>
        </div>

        <div>
          <button 
            type="submit" 
            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            :disabled="loading"
          >
            <span v-if="loading" class="absolute left-0 inset-y-0 flex items-center pl-3">
              <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </span>
            {{ loading ? 'Duke u regjistruar...' : 'Regjistrohu' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import { ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

export default {
  name: 'Register',
  setup() {
    const router = useRouter()
    const authStore = useAuthStore()
    
    const userType = ref('')
    const firstName = ref('')
    const lastName = ref('')
    const email = ref('')
    const password1 = ref('')
    const password2 = ref('')
    const businessName = ref('')
    const agreeToTerms = ref(false)
    const loading = ref(false)
    const error = ref('')
    
    // Validate passwords match
    watch(password2, (newValue) => {
      if (newValue && password1.value && newValue !== password1.value) {
        error.value = 'Fjalëkalimet nuk përputhen'
      } else {
        error.value = ''
      }
    })
    
    const handleRegister = async () => {
      // Validate form
      if (password1.value !== password2.value) {
        error.value = 'Fjalëkalimet nuk përputhen'
        return
      }
      
      loading.value = true
      error.value = ''
      
      try {
        const userData = {
          email: email.value,
          password1: password1.value,
          password2: password2.value,
          first_name: firstName.value,
          last_name: lastName.value,
          user_type: userType.value
        }
        
        if (userType.value === 'vendor') {
          userData.business_name = businessName.value
        }
        
        await authStore.register(userData)
        
        // Redirect based on user type
        if (userType.value === 'vendor') {
          router.push('/dashboard')
        } else {
          router.push('/')
        }
      } catch (err) {
        console.error('Registration error:', err)
        if (err.response?.data) {
          // Format API validation errors
          const apiErrors = err.response.data
          const errorMessages = []
          
          for (const field in apiErrors) {
            if (Array.isArray(apiErrors[field])) {
              errorMessages.push(`${field}: ${apiErrors[field].join(', ')}`)
            }
          }
          
          error.value = errorMessages.join('. ') || 'Gabim gjatë regjistrimit. Ju lutemi provoni përsëri.'
        } else {
          error.value = 'Gabim gjatë regjistrimit. Ju lutemi provoni përsëri.'
        }
      } finally {
        loading.value = false
      }
    }
    
    return {
      userType,
      firstName,
      lastName,
      email,
      password1,
      password2,
      businessName,
      agreeToTerms,
      loading,
      error,
      handleRegister
    }
  }
}
</script>
