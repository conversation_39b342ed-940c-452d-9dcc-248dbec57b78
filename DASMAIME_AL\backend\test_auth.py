import requests
import json

# Test JWT authentication
def test_jwt_auth():
    print("Testing JWT authentication...")
    url = "http://localhost:8000/api/token/"
    data = {
        "username": "admin",
        "password": "admin"
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"Status code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            token = response.json().get('access')
            print(f"JWT Token: {token[:20]}...")
            return token
        else:
            print("JWT authentication failed")
            return None
    except Exception as e:
        print(f"Error: {e}")
        return None

# Test dj-rest-auth authentication
def test_dj_rest_auth():
    print("\nTesting dj-rest-auth authentication...")
    url = "http://localhost:8000/api/auth/login/"
    data = {
        "username": "admin",
        "password": "admin"
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"Status code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            key = response.json().get('key')
            print(f"Auth Key: {key[:20] if key else 'None'}...")
            return key
        else:
            print("dj-rest-auth authentication failed")
            return None
    except Exception as e:
        print(f"Error: {e}")
        return None

# Test with email
def test_with_email():
    print("\nTesting authentication with email...")
    url = "http://localhost:8000/api/token/"
    data = {
        "email": "<EMAIL>",
        "password": "admin"
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"Status code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            token = response.json().get('access')
            print(f"JWT Token: {token[:20]}...")
            return token
        else:
            print("JWT authentication with email failed")
            return None
    except Exception as e:
        print(f"Error: {e}")
        return None

# Test user info
def test_user_info(token):
    if not token:
        print("\nSkipping user info test (no token)")
        return
    
    print("\nTesting user info endpoint...")
    url = "http://localhost:8000/api/auth/user/"
    headers = {
        "Authorization": f"Bearer {token}"
    }
    
    try:
        response = requests.get(url, headers=headers)
        print(f"Status code: {response.status_code}")
        print(f"Response: {response.text}")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    # Test JWT auth
    token = test_jwt_auth()
    
    # Test dj-rest-auth
    key = test_dj_rest_auth()
    
    # Test with email
    email_token = test_with_email()
    
    # Test user info
    test_user_info(token or email_token)
