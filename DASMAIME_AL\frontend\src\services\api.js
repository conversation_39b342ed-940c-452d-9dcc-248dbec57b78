import axios from 'axios'

// <PERSON><PERSON>jo instance të axios me konfigurimin bazë
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:8000',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  }
})

// Request interceptor për të shtuar token-in në header
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token')
    if (token) {
      // Check if it's a JWT token (they typically contain dots)
      if (token.includes('.')) {
        config.headers.Authorization = `Bearer ${token}`
      } else {
        // For dj-rest-auth token format
        config.headers.Authorization = `Token ${token}`
      }
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor për të menaxhuar përgjigjet dhe gabimet
api.interceptors.response.use(
  (response) => {
    return response
  },
  async (error) => {
    const originalRequest = error.config

    // Nëse token-i ka skaduar (401 error)
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true

      try {
        const refreshToken = localStorage.getItem('refresh_token')
        if (refreshToken) {
          const response = await axios.post(
            `${import.meta.env.VITE_API_URL || 'http://localhost:8000'}/api/token/refresh/`,
            { refresh: refreshToken }
          )
          const newAccessToken = response.data.access
          localStorage.setItem('access_token', newAccessToken)

          // Riprovo kërkesën origjinale me token-in e ri
          originalRequest.headers.Authorization = `Bearer ${newAccessToken}`
          return api(originalRequest)
        }
      } catch (refreshError) {
        // Nëse refresh token-i është i pavlefshëm, çkyç përdoruesin
        localStorage.removeItem('access_token')
        localStorage.removeItem('refresh_token')
        window.location.href = '/login'
        return Promise.reject(refreshError)
      }
    }

    return Promise.reject(error)
  }
)

export default api