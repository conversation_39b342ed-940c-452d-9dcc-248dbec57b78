import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

// Import views
import Home from '@/views/Home.vue'
import VendorDetail from '@/views/VendorDetail.vue'
import Categories from '@/views/Categories.vue'
import CategoryDetail from '@/views/CategoryDetail.vue'
import Login from '@/views/Login.vue'
import Register from '@/views/Register.vue'
import VendorDashboard from '@/views/VendorDashboard.vue'
import NotFound from '@/views/NotFound.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: { title: 'DasmalmeAL - Gjeni Furnitorët Më të Mirë për Dasmën Tuaj' }
  },
  {
    path: '/vendors/:id',
    name: 'VendorDetail',
    component: VendorDetail,
    props: true,
    meta: { title: 'Detajet e Furnitorit' }
  },
  {
    path: '/categories',
    name: 'Categories',
    component: Categories,
    meta: { title: 'Kategoritë e Furnitorëve - DasmalmeAL' }
  },
  {
    path: '/category/:slug',
    name: 'CategoryDetail',
    component: CategoryDetail,
    props: true,
    meta: { title: 'Furnitorë sipas Kategorisë' }
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: {
      title: 'Identifikohu - DasmalmeAL',
      guest: true
    }
  },
  {
    path: '/register',
    name: 'Register',
    component: Register,
    meta: {
      title: 'Regjistrohuni - DasmalmeAL',
      guest: true
    }
  },  {
    path: '/dashboard',
    name: 'VendorDashboard',
    component: VendorDashboard,
    meta: {
      title: 'Paneli i Furnitorit - DasmalmeAL',
      requiresAuth: true,
      vendorOnly: true
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFound,
    meta: { title: 'Faqja nuk u gjet - DasmalmeAL' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// Navigation guards
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()

  // Set page title
  document.title = to.meta.title || 'DasmalmeAL'

  // Check if route requires authentication
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next({ name: 'Login', query: { redirect: to.fullPath } })
    return
  }

  // Check if route is for vendors only
  if (to.meta.vendorOnly && authStore.user?.user_type !== 'vendor') {
    next({ name: 'Home' })
    return
  }

  // Check if route is for guests only (login/register)
  if (to.meta.guest && authStore.isAuthenticated) {
    next({ name: 'Home' })
    return
  }

  next()
})

export default router