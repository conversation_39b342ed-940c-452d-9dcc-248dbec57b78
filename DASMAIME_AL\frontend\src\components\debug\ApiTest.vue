<template>
  <div class="api-test p-4 bg-yellow-100 border border-yellow-400 rounded">
    <h3 class="text-lg font-bold mb-4">API Test Debug</h3>
    
    <div class="space-y-4">
      <div>
        <button @click="testCategoriesAPI" class="bg-blue-500 text-white px-4 py-2 rounded">
          Test Categories API
        </button>
        <div v-if="categoriesResult" class="mt-2 p-2 bg-gray-100 rounded">
          <pre>{{ categoriesResult }}</pre>
        </div>
      </div>
      
      <div>
        <button @click="testVendorsAPI" class="bg-green-500 text-white px-4 py-2 rounded">
          Test Vendors API
        </button>
        <div v-if="vendorsResult" class="mt-2 p-2 bg-gray-100 rounded">
          <pre>{{ vendorsResult }}</pre>
        </div>
      </div>
      
      <div>
        <button @click="testStores" class="bg-purple-500 text-white px-4 py-2 rounded">
          Test Stores
        </button>
        <div v-if="storesResult" class="mt-2 p-2 bg-gray-100 rounded">
          <pre>{{ storesResult }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { categoryService } from '@/services/categories'
import { vendorService } from '@/services/vendors'
import { useCategoryStore } from '@/stores/categories'
import { useVendorStore } from '@/stores/vendors'

const categoriesResult = ref(null)
const vendorsResult = ref(null)
const storesResult = ref(null)

const categoryStore = useCategoryStore()
const vendorStore = useVendorStore()

const testCategoriesAPI = async () => {
  try {
    categoriesResult.value = 'Loading...'
    const response = await categoryService.getCategories()
    categoriesResult.value = JSON.stringify({
      success: true,
      data: response,
      count: response?.results?.length || response?.length || 0
    }, null, 2)
  } catch (error) {
    categoriesResult.value = JSON.stringify({
      success: false,
      error: error.message,
      stack: error.stack
    }, null, 2)
  }
}

const testVendorsAPI = async () => {
  try {
    vendorsResult.value = 'Loading...'
    const response = await vendorService.getVendors()
    vendorsResult.value = JSON.stringify({
      success: true,
      data: response,
      count: response?.results?.length || response?.length || 0
    }, null, 2)
  } catch (error) {
    vendorsResult.value = JSON.stringify({
      success: false,
      error: error.message,
      stack: error.stack
    }, null, 2)
  }
}

const testStores = async () => {
  try {
    storesResult.value = 'Loading...'
    
    // Test category store
    await categoryStore.fetchCategories()
    
    // Test vendor store
    await vendorStore.fetchVendors()
    
    storesResult.value = JSON.stringify({
      success: true,
      categories: {
        count: categoryStore.categories.length,
        loading: categoryStore.loading,
        error: categoryStore.error,
        data: categoryStore.categories.slice(0, 2) // First 2 for brevity
      },
      vendors: {
        count: vendorStore.vendors.length,
        loading: vendorStore.loading,
        error: vendorStore.error,
        data: vendorStore.vendors.slice(0, 2) // First 2 for brevity
      }
    }, null, 2)
  } catch (error) {
    storesResult.value = JSON.stringify({
      success: false,
      error: error.message,
      stack: error.stack
    }, null, 2)
  }
}
</script>

<style scoped>
pre {
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
  white-space: pre-wrap;
}
</style>
