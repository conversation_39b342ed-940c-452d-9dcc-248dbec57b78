<template>
  <div class="min-h-screen">
    <!-- Debug Component (only in development) -->
    <ApiTest v-if="isDevelopment" />

    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-primary-500 to-primary-700 text-white py-20">
      <div class="container-custom text-center">
        <h1 class="text-4xl md:text-6xl font-heading font-bold mb-6">
          Gjeni Fu<PERSON>itor<PERSON>t Idealë për Dasmën <PERSON>
        </h1>
        <p class="text-xl mb-8 opacity-90">
          Platforma më e mirë për furnitorët e dasmave në Shqipëri
        </p>

        <!-- Search Bar -->
        <div class="max-w-2xl mx-auto flex gap-4">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Kërkoni furnitorë..."
            class="input-field text-gray-900 flex-1"
          />
          <button
            @click="searchVendors"
            class="bg-white text-primary-600 hover:bg-gray-100 font-medium py-3 px-6 rounded-lg"
          >
            Kërko
          </button>
        </div>
      </div>
    </section>

    <!-- Categories Section -->
    <section class="py-16 bg-white">
      <div class="container-custom">
        <h2 class="section-title text-center">Kategoritë e Furnitorëve</h2>

        <div v-if="Array.isArray(categoryStore.categories) && categoryStore.categories.length > 0" class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          <CategoryCard
            v-for="category in categoryStore.categories.slice(0, 8)"
            :key="category?.id || Math.random()"
            :category="category"
            size="medium"
            variant="primary"
            :centered="true"
            :show-description="false"
            :show-count="true"
            @click="goToCategory(category?.slug)"
          />
        </div>

        <div v-else-if="!categoryStore.loading" class="text-center py-8">
          <p class="text-gray-600">Nuk ka kategori të disponueshme</p>
        </div>

        <div class="text-center" v-if="Array.isArray(categoryStore.categories) && categoryStore.categories.length > 8">
          <router-link to="/categories" class="btn-primary inline-block">
            Shiko të gjitha kategoritë
          </router-link>
        </div>
      </div>
    </section>

    <!-- Popular Categories Section -->
    <section class="py-16 bg-gray-100">
      <div class="container-custom">
        <h2 class="section-title text-center">Kategoritë Më të Populluara</h2>

        <div v-if="Array.isArray(popularCategories) && popularCategories.length > 0" class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <CategoryCard
            v-for="category in popularCategories"
            :key="category?.id || Math.random()"
            :category="category"
            size="medium"
            variant="neutral"
            :centered="false"
            :show-description="true"
            :show-count="true"
            @click="goToCategory(category?.slug)"
          />
        </div>

        <div v-else class="text-center py-8">
          <p class="text-gray-600">Nuk ka kategori të populluara</p>
        </div>
      </div>
    </section>

    <!-- Vendors Section -->
    <section class="py-16 bg-gray-50">
      <div class="container-custom">
        <h2 class="section-title text-center">Furnitorët Tanë</h2>

        <div class="flex flex-col md:flex-row gap-8">
          <!-- Category Filter Sidebar -->
          <div class="w-full md:w-1/4">
            <div class="bg-white rounded-lg shadow-md p-4">
              <CategoryFilter
                v-model="selectedCategory"
                :total-vendors="vendorStore.vendors.length"
                @update:model-value="filterVendors"
              />
            </div>
          </div>

          <!-- Vendors Grid -->
          <div class="w-full md:w-3/4">
            <div v-if="vendorStore.loading" class="text-center py-12">
              <div class="loading-spinner mx-auto"></div>
              <p class="mt-2 text-gray-600">Duke ngarkuar...</p>
            </div>

            <div v-else-if="vendorStore.vendors.length === 0" class="text-center py-12">
              <p class="text-gray-600 mb-4">Nuk u gjetën furnitorë.</p>
              <button @click="resetFilters" class="btn-primary">
                Rivendos filtrat
              </button>
            </div>

            <div v-else class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div
                v-for="vendor in vendorStore.vendors"
                :key="vendor.id"
                @click="goToVendor(vendor.id)"
                class="vendor-card p-6"
              >
                <div class="flex items-center mb-3">
                  <i :class="getCategoryIcon(vendor.category_slug) || 'fas fa-list'" class="text-xl text-primary-500 mr-3"></i>
                  <h3 class="font-semibold">{{ vendor.business_name }}</h3>
                </div>
                <p class="text-gray-600 text-sm mb-2">{{ vendor.category_name }}</p>
                <p class="text-gray-600 text-sm">{{ vendor.city }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>
<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useVendorStore } from '@/stores/vendors'
import { useCategoryStore } from '@/stores/categories'
import CategoryCard from '@/components/category/CategoryCard.vue'
import CategoryFilter from '@/components/category/CategoryFilter.vue'
import ApiTest from '@/components/debug/ApiTest.vue'
import { fallbackCategories, fallbackVendors } from '@/utils/fallbackData'

const router = useRouter()
const vendorStore = useVendorStore()
const categoryStore = useCategoryStore()

const searchQuery = ref('')
const selectedCategory = ref('')

// Check if we're in development mode
const isDevelopment = import.meta.env.VITE_ENV === 'development' || import.meta.env.DEV

// Get the 3 most popular categories
const popularCategories = computed(() => {
  if (!Array.isArray(categoryStore.categories)) return []
  return categoryStore.popularCategories.slice(0, 3)
})

onMounted(async () => {
  try {
    console.log('Home component mounted, loading data...')
    await categoryStore.fetchCategories()
    await vendorStore.fetchVendors()
    console.log('Data loaded. Categories:', categoryStore.categories.length, 'Vendors:', vendorStore.vendors.length)

    // Force fallback if no categories loaded
    if (!Array.isArray(categoryStore.categories) || categoryStore.categories.length === 0) {
      console.warn('No categories loaded, forcing fallback data')
      categoryStore.categories = [...fallbackCategories]
    }

    // Force fallback if no vendors loaded
    if (!Array.isArray(vendorStore.vendors) || vendorStore.vendors.length === 0) {
      console.warn('No vendors loaded, forcing fallback data')
      vendorStore.vendors = [...fallbackVendors]
    }
  } catch (error) {
    console.error('Error loading data:', error)
  }
})

async function filterVendors() {
  const params = {}
  if (searchQuery.value) params.search = searchQuery.value
  if (selectedCategory.value) params.category__slug = selectedCategory.value

  try {
    await vendorStore.fetchVendors(params)
  } catch (error) {
    console.error('Error searching vendors:', error)
  }
}

function resetFilters() {
  selectedCategory.value = ''
  searchQuery.value = ''
  vendorStore.fetchVendors()
}

function goToCategory(categorySlug) {
  router.push(`/category/${categorySlug}`)
}

function goToVendor(vendorId) {
  router.push(`/vendors/${vendorId}`)
}

// Get category icon by slug
function getCategoryIcon(slug) {
  if (!slug || !Array.isArray(categoryStore.categories)) return 'fas fa-list'
  const category = categoryStore.categories.find(cat => cat?.slug === slug)
  return category?.icon || 'fas fa-list'
}
</script>