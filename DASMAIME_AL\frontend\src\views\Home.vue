<template>
  <div class="min-h-screen">
    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-primary-500 to-primary-700 text-white py-20">
      <div class="container-custom text-center">
        <h1 class="text-4xl md:text-6xl font-heading font-bold mb-6">
          <PERSON><PERSON><PERSON>t Idealë për <PERSON>
        </h1>
        <p class="text-xl mb-8 opacity-90">
          Platforma më e mirë për furnitorët e dasmave në Shqipëri
        </p>

        <!-- Search Bar -->
        <div class="max-w-2xl mx-auto flex gap-4">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Kërkoni furnitorë..."
            class="input-field text-gray-900 flex-1"
          />
          <button
            @click="searchVendors"
            class="bg-white text-primary-600 hover:bg-gray-100 font-medium py-3 px-6 rounded-lg"
          >
            <PERSON><PERSON><PERSON><PERSON>
          </button>
        </div>
      </div>
    </section>

    <!-- Categories Section -->
    <section class="py-16 bg-white">
      <div class="container-custom">
        <h2 class="section-title text-center">Kategoritë e Furnitorëve</h2>

        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <button
            v-for="category in categoryStore.categories"
            :key="category.id"
            @click="filterByCategory(category.slug)"
            class="card p-6 text-center hover:shadow-lg transition-shadow"
          >
            <h3 class="font-semibold text-gray-900">{{ category.name }}</h3>
            <p class="text-sm text-gray-600 mt-1">
              {{ category.vendor_count }} furnitorë
            </p>
          </button>
        </div>
      </div>
    </section>

    <!-- Vendors Section -->
    <section class="py-16 bg-gray-50">
      <div class="container-custom">
        <h2 class="section-title text-center">Furnitorët Tanë</h2>

        <div v-if="vendorStore.loading" class="text-center">
          <div class="loading-spinner mx-auto"></div>
          <p class="mt-2 text-gray-600">Duke ngarkuar...</p>
        </div>

        <div v-else class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div
            v-for="vendor in vendorStore.vendors"
            :key="vendor.id"
            @click="goToVendor(vendor.id)"
            class="vendor-card p-6"
          >
            <h3 class="font-semibold mb-2">{{ vendor.business_name }}</h3>
            <p class="text-gray-600 text-sm mb-2">{{ vendor.category_name }}</p>
            <p class="text-gray-600 text-sm">{{ vendor.city }}</p>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>
<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useVendorStore } from '@/stores/vendors'
import { useCategoryStore } from '@/stores/categories'

const router = useRouter()
const vendorStore = useVendorStore()
const categoryStore = useCategoryStore()

const searchQuery = ref('')
const selectedCategory = ref('')

onMounted(async () => {
  try {
    await vendorStore.fetchVendors()
    await categoryStore.fetchCategories()
  } catch (error) {
    console.error('Error loading data:', error)
  }
})

async function searchVendors() {
  const params = {}
  if (searchQuery.value) params.search = searchQuery.value
  if (selectedCategory.value) params.category__slug = selectedCategory.value

  try {
    await vendorStore.fetchVendors(params)
  } catch (error) {
    console.error('Error searching vendors:', error)
  }
}

function filterByCategory(categorySlug) {
  selectedCategory.value = categorySlug
  searchVendors()
}

function goToVendor(vendorId) {
  router.push(`/vendors/${vendorId}`)
}
</script>